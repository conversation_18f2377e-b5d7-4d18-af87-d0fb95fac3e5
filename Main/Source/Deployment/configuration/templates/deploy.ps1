{%- for key, value in context().items() %}
{%- if not callable(value) %}
Set-OctopusVariable -name "#{ key }" -value "#{ value }"
{%- endif %}
{%- endfor %}

<# 
This file was rendered at #{ __CURRENT_TIME__ }
The following sensitive variables are stored in the Octopus project used to deploy this application:
{% raw %}
{%- for key, value in context().items() %}
{%- if not callable(value) %}
    {{ key }}
{%- endif %}
{%- endfor %}
{%- endraw %}
#>
