﻿<?xml version="1.0" encoding="utf-8"?>
<!-- 
 !!! DEVELOPER WARNING !!!
 THIS FILE IS AUTOGENERATED ON EVERY BUILD
 IF YOU WANT TO DO CHANGES THE TEMPLATE LOCATION IS: /Deployment/configuration/templates/web.config
 THE TOKENIZED ENVIRONMENT SPECIFIC PROPERTY FILES ARE IN: /Deployment/configuration/
 GENERATED AT: #{ __CURRENT_TIME__ }
-->
<configuration>
  <configSections>
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data" />
	<section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->

  </configSections>	

	<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
	<loggingConfiguration name="" tracingEnabled="true" defaultCategory="General">
		<listeners>
			<add name="SSDefaultEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSDefault" formatter="EventLogTextFormatter" log="SSDefault" machineName="." traceOutputOptions="None" filter="Warning" />
			<add name="SSWebPortalEventLog" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" source="SSWebPortal" formatter="EventLogTextFormatter" log="SSWebPortal" filter="Warning" />
			<add name="DefaultTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\defaultTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
			<add name="WebPortalTrace" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.RollingFlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.RollingFlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" fileName="C:\AppLogs\SmartSort\webportalTrace.log" formatter="DebugFormatter" rollFileExistsBehavior="Increment" rollInterval="Day" rollSizeKB="10240" maxArchivedFiles="20" />
		</listeners>
		<formatters>
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="EventLogTextFormatter" />
			<add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" template="Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Category: {category}{newline}&#xA;Priority: {priority}{newline}&#xA;EventId: {eventid}{newline}&#xA;Severity: {severity}{newline}&#xA;Title:{title}{newline}&#xA;Machine: {localMachine}{newline}&#xA;App Domain: {localAppDomain}{newline}&#xA;ProcessId: {localProcessId}{newline}&#xA;Process Name: {localProcessName}{newline}&#xA;Thread Name: {threadName}{newline}&#xA;Win32 ThreadId:{win32ThreadId}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}" name="DebugFormatter" />
		</formatters>
		<categorySources>
			<add switchValue="All" name="General">
				<listeners>
					<add name="SSDefaultEventLog" />
					<add name="DefaultTrace" />
				</listeners>
			</add>
			<add switchValue="All" name="WebPortal">
				<listeners>
					<add name="SSWebPortalEventLog" />
					<add name="WebPortalTrace" />
				</listeners>
			</add>
		</categorySources>
		<specialSources>
			<allEvents switchValue="All" name="All Events" />
			<notProcessed switchValue="All" name="Unprocessed Category" />
			<errors switchValue="All" name="Logging Errors &amp; Warnings">
				<listeners>
					<add name="SSDefaultEventLog" />
				</listeners>
			</errors>
		</specialSources>
	</loggingConfiguration>
	<appSettings>
    <add key="aspnet:MaxJsonDeserializerMembers" value="9999999" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />  
    <add key="DATABASE" value="SmartSortConnection" />
    <add key="AllowedUserEmailDomains" value="innovapost.com, purolator.com, cpggpc.ca" />
    <add key="DomainController" value="#{DOMAIN_CONTROLLER}" />
    <add key="ValidateStickySession" value="true" />
  </appSettings>
  <system.web>
    <globalization culture="auto" uiCulture="auto" enableClientBasedCulture="true" />
    <compilation debug="#{DEBUG}" targetFramework="4.5">
      <assemblies>
        <add assembly="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.5" />
    <pages pageBaseType="System.Web.Mvc.WebViewPage">
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Optimization" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
    </pages>
    <sessionState mode="Custom" customProvider="SqlInMemoryProvider" timeout="30">
      <providers>
        <add name="SqlInMemoryProvider" type="Microsoft.Web.SessionState.SqlInMemoryProvider" connectionString="#{SESSIONDB}" />
      </providers>
    </sessionState>
	<machineKey validationKey="#{VALIDATION_KEY}" decryptionKey="#{DECRYPTION_KEY}" validation="SHA1"/>
  </system.web>
  <system.webServer>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_ISSOService" />
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://localhost:8080/SSO.svc" binding="basicHttpBinding"
				bindingConfiguration="BasicHttpBinding_ISSOService" contract="SSOServiceReference.ISSOService"
				name="BasicHttpBinding_ISSOService" />
		</client>
	</system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages.Razor" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Data" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5" />
  </startup>
  <dataConfiguration defaultDatabase="SmartSortConnection" />
  <connectionStrings>
	  <add name="SmartSortConnection" connectionString="#{SSDB}" providerName="System.Data.SqlClient" /> 
  </connectionStrings>
</configuration>
