﻿
namespace SmartSort.DataAccessLayer
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using SmartSort.Common;
    using SmartSort.Business.Entities;

    public class AddressTriagingRepository
    {

       DataTable objDataTable=null;
      
        Boolean saveStatus = false;
        List<SmartSort.DataAccessLayer.ParametersRepository> objSPListParam; 
        List<SmartSort.DataAccessLayer.ParametersRepository> objSPListOutParam; 

        /// <summary>
        /// Getting AddressTriage record from Database.
        /// </summary>
        /// <param name="addressRequest"></param>
        /// <returns> Datatable for AddressTriage Records</returns>
        public DataTable GetAddressTriageRecord(AddressRequest addressRequest, out int noOfRecords)
        {

            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            objSPListOutParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            //NoofRecords = 0;
             noOfRecords = 0;
            if (null != addressRequest)
            {
                try  
                {

                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 50, ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = addressRequest.Terminal });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 10, ParamDataType = DbType.String, ParamName = "@EscalatedAddressOnly", ParamValue = addressRequest.EscalatedAddress });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@PageNumber", ParamValue = addressRequest.PageNumber });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@RowsPerPage", ParamValue = addressRequest.RowsPerPage });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 30,ParamDataType = DbType.String,ParamName = "@SortColumn", ParamValue = addressRequest.SortCriteria });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 30, ParamDataType = DbType.String, ParamName = "@SortDirection", ParamValue = "ASC" });


                    objSPListOutParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@NoOfAddresses" });

                    objDataTable = CommonRepository.GetDataTable("[dbo].[GetTriageQueue]", out noOfRecords, objSPListParam, objSPListOutParam);

                }
                catch (SqlException ex)
                {
                    SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetAddressTriagerecord = " + ((addressRequest.Terminal != null) ? addressRequest.Terminal.ToString() : string.Empty) + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                     SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
                }
                finally
                {
                    objSPListParam = null;
                    objSPListOutParam = null;
                }
            }

            return objDataTable;
        }

        /// <summary>
        /// Getting Address based on Id.
        /// </summary>
        /// <param name="AddressID"></param>
        public DataTable GetAddress(int TriageQueueID)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = TriageQueueID });
                //TO DO: Update SP name below
                objDataTable = CommonRepository.GetDataTable("[dbo].[GetAddresses]", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetAddress = " + TriageQueueID.ToString() + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting Triage PIN Details.
        /// </summary>
        /// <param name="TriageQueueID"></param>
        /// <returns>IDataReader PIN Details</returns>
        public DataTable GetTriagePINDetails(int TriageQueueID)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = TriageQueueID });
                //TO DO: Update SP name below
                objDataTable = CommonRepository.GetDataTable("[dbo].[GetTriagePins]", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetAddress = " + TriageQueueID.ToString() + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Updating Triage Address in Database.
        /// </summary>
        /// <param name="addressDetails"></param>
        /// <param name="Status"></param>
        /// <param name="Route"></param>
        /// <param name="Shelf"></param>
        /// <returns>Boolean message</returns>
        public Boolean UpdateTriageAddress(AddressDetails addressDetails, out string Status, out string Route, out string Shelf)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            objSPListOutParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            Status =string.Empty;
            Route = string.Empty;
            Shelf = string.Empty;
            if (null != addressDetails)
            {
                try
                {
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = addressDetails.TriageQueueID });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 6, ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = addressDetails.Terminal });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 40, ParamDataType = DbType.String, ParamName = "@CompanyName", ParamValue = addressDetails.CurrentCompanyName });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 10, ParamDataType = DbType.String, ParamName = "@StreetNumber", ParamValue = addressDetails.StreetNumber });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 1, ParamDataType = DbType.String, ParamName = "@StreetNumSuf", ParamValue = addressDetails.CurrentStreetNumSuf });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 10, ParamDataType = DbType.String, ParamName = "@Suite", ParamValue = addressDetails.SuiteNumber });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 40, ParamDataType = DbType.String, ParamName = "@StreetName", ParamValue = addressDetails.StreetName });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 6, ParamDataType = DbType.String, ParamName = "@StreetType", ParamValue = addressDetails.StreetType });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 2, ParamDataType = DbType.String, ParamName = "@StreetDir", ParamValue = addressDetails.StreetDirection });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 60, ParamDataType = DbType.String, ParamName = "@City", ParamValue = addressDetails.City });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 10, ParamDataType = DbType.String, ParamName = "@PostalCode", ParamValue = addressDetails.PostalCode });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 3, ParamDataType = DbType.String, ParamName = "@Province", ParamValue = addressDetails.Province });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 50, ParamDataType = DbType.String, ParamName = "@UserName", ParamValue = addressDetails.UserName });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@Pieces", ParamValue = addressDetails.Pieces });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataSize = 10, ParamDataType = DbType.String, ParamName = "@TriageReasonCode", ParamValue = addressDetails.TriageReasonCode });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.DateTime, ParamName = "@UpdateDateTimeOffset", ParamValue = addressDetails.UpdateDateTimeOffset });
                    //List of OutPut Parameters 
                objSPListOutParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Status", ParamDataSize=10 });
                objSPListOutParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Route", ParamDataSize = 6 });
                objSPListOutParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Shelf", ParamDataSize = 2 });
                //List of Out Parameter              


                 

                saveStatus=(CommonRepository.SaveData("UpdateTriageAddress",objSPListParam, objSPListOutParam));
                }
                catch (SqlException ex)
                {
                    SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on UpdateTriageAddress = " + Convert.ToString(addressDetails.TriageQueueID) + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                    SmartSortException ssException = new SmartSortException(error);
                    SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
                }
                finally
                {
                    objSPListParam = null;
                    objSPListOutParam = null;
                }
            }
            return saveStatus;

        }

        /// <summary>
        /// Update Triage Queue.
        /// </summary>
        /// <param name="TriageQueueID"></param>
        /// <param name="UserName"></param>
        /// <returns>IDataReader</returns>
        public Boolean UpdateTriageLastTouch(int TriageQueueID, string UserName, DateTimeOffset UpdateDateTimeOffset)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = TriageQueueID });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository {ParamDataSize=10,ParamDataType = DbType.String, ParamName = "@UserName", ParamValue = UserName });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.DateTime, ParamName = "@@UpdateDateTimeOffset", ParamValue = UpdateDateTimeOffset });
                saveStatus = CommonRepository.SaveData("UpdateTriageLastTouch", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on UpdateTriageLastTouch = " + Convert.ToString(TriageQueueID) + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return saveStatus;
        }

        /// <summary>
        /// Getting Triage ReasonCodes.
        /// </summary>
        /// <returns>DataTable TriageReasonCodes</returns>
        public DataTable GetTriageReasonCodes()
        {
            try
            {
                objDataTable = CommonRepository.GetDataTable("Gettriagereasoncodes",null);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetTriageReasonCodes " + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
            }
            return objDataTable;
        }

        /// <summary>
        /// UpdatePinMaster DB Call
        /// </summary>
        /// <param name="TriageQueueID">TriageQueueID</param>
        /// <param name="Route">Route</param>
        /// <param name="Shelf">Shelf</param>
        /// <param name="UserName">UserName</param>
        /// <param name="UpdateDateTimeOffset">UpdateDateTimeOffset</param>
        /// <returns>boolean</returns>
        public Boolean UpdatePINMaster(int TriageQueueID, string Route, string Shelf, string UserName, DateTime UpdateDateTimeOffset)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = TriageQueueID });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@route", ParamValue = Route });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@shelf", ParamValue = Shelf });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@UserName", ParamValue = Shelf });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@UpdateDateTimeOffset", ParamValue = Shelf });
                saveStatus = CommonRepository.SaveData("UpdatePINMaster", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on UpdatePINMaster = "+Convert.ToString(TriageQueueID) + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return saveStatus;
        }

        /// <summary>
        /// Getting Address From RoutePlan based on Terminal.
        /// </summary>
        /// <param name="Terminal">Terminal</param>
        /// <param name="PostalCode">PostalCode</param>
        /// <returns>Return DataTable</returns>
        public DataTable GetAddressFromRoutePlan(string Terminal,string PostalCode)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@postalcode", ParamValue = PostalCode });
                objDataTable = CommonRepository.GetDataTable("GetAddressFromRoutePlan", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetAddressFromRoutePlan = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting the Street name based in Terminal
        /// </summary>
        /// <param name="Terminal"></param>
        public DataTable GetStreetNameFromRoutePlan(string Terminal, string streetName)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@streetName", ParamValue = streetName });
                objDataTable = CommonRepository.GetDataTable("GetStreetNameFromRoutePlan", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetStreetNameFromRoutePlan = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting All Routes and Shelf's based on Terminal
        /// </summary>
        /// <param name="Terminal"></param>
        public DataTable GetAllRouteAndShelfFromRoutePlan(string Terminal)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objDataTable = CommonRepository.GetDataTable("GetAllRouteAndShelfFromRoutePlan", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetAllRouteAndShelfFromRoutePlan = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

       /// <summary>
        /// Updating Route and Shelf for a Terminal.
       /// </summary>
       /// <param name="addressDetails"></param>
       /// <param name="Status"></param>
       /// <returns></returns>
        public string UpdateRouteAndShelf(AddressDetails addressDetails, out string Status)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            objSPListOutParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            string result = string.Empty;
            Status = string.Empty;
            if (null != addressDetails)
            {
                try  
                {
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@TriageQueueID", ParamValue = addressDetails.TriageQueueID });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Route", ParamValue = addressDetails.Route });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Shelf", ParamValue = addressDetails.Shelf });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.Int32, ParamName = "@Pieces", ParamValue = addressDetails.Pieces });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@UserName", ParamValue = addressDetails.UserName });
                    objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.DateTimeOffset, ParamName = "@UpdateDateTimeOffset", ParamValue = addressDetails.UpdateDateTimeOffset });
                    objSPListOutParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Status", ParamDataSize = 10 });

                    result= CommonRepository.SaveData("UpdateRouteAndShelf", out Status, objSPListParam, objSPListOutParam);
                }
                catch (SqlException ex)
                {
                    SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on UpdateRouteAndShelf = " + addressDetails.TriageQueueID + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                    SmartSortException ssException = new SmartSortException(error);
                    SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
                }
                finally
                {
                    objSPListParam = null;
                    objSPListOutParam = null;
                }
            }
            return result;
        }

        /// <summary>
        /// Getting Provinces List for a Terminal.
        /// </summary>
        /// <param name="Terminal"></param>
        /// <returns>DataTable Provinces List</returns>
        public DataTable GetProvincesList(string Terminal)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objDataTable = CommonRepository.GetDataTable("GetProvinces", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetProvincesList = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting Street Suffix List for Terminal.
        /// </summary>
        /// <param name="Terminal"></param>
        /// <returns>DataTable StreetSuffix List</returns>
        public DataTable GetStreetSuffixList(string Terminal)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objDataTable = CommonRepository.GetDataTable("GetStreetSuffix", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetStreetSuffixList = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting Street Direction List for Terminal.
        /// </summary>
        /// <param name="Terminal"></param>
        /// <returns>DataTable StreetDirection List</returns>
        public DataTable GetStreetDirectionList(string Terminal)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objDataTable = CommonRepository.GetDataTable("GetStreetDirection", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetStreetDirectionList = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }

        /// <summary>
        /// Getting Street Types List for Terminal.
        /// </summary>
        /// <param name="Terminal"></param>
        /// <returns>DataTable StreetTypesList</returns>
        public DataTable GetStreetTypesList(string Terminal)
        {
            objSPListParam = new List<SmartSort.DataAccessLayer.ParametersRepository>();
            try
            {
                objSPListParam.Add(new SmartSort.DataAccessLayer.ParametersRepository { ParamDataType = DbType.String, ParamName = "@Terminal", ParamValue = Terminal });
                objDataTable = CommonRepository.GetDataTable("GetStreetTypes", objSPListParam);
            }
            catch (SqlException ex)
            {
                SmartSortError error = new SmartSortError("AddressTraiging Repository : Exception on GetStreetTypesList = " + Terminal + "Error: " + ex.Message + "Stack Trace: " + ex.StackTrace);
                SmartSortException ssException = new SmartSortException(error);
                SmartSortLogger.Write(error.Message, Constants.EXCEPTION_CATEGORY_DATA_ACCESS, LogPriority.High, System.Diagnostics.TraceEventType.Error);
            }
            finally
            {
                objSPListParam = null;
            }
            return objDataTable;
        }
    }
}
