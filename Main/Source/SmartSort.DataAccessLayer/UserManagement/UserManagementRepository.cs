﻿using System;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class UserManagementRepository : Repository<UserProfileDetails>
    {
        #region Public Constructors
        public UserManagementRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion

        #region Public Methods
        public DataTable GetAssignableRoles(int currentUserId)
        {
            ISelectionFactory<UserProfileDetails> selectionFactory = new GetAssignableRolesSelectionFactory();
            UserProfileDetails userProfileDetails = new UserProfileDetails() { CurrentUserId = currentUserId };
            
            return Find(selectionFactory, userProfileDetails);
        }

        public DataTable GetUserTerminals(int currentUserId)
        {
            ISelectionFactory<UserProfileDetails> selectionFactory = new GetUserTerminalsSelectionFactory();
            UserProfileDetails userProfileDetails = new UserProfileDetails() { CurrentUserId = currentUserId };
            
            return Find(selectionFactory, userProfileDetails);
        }

        public DataTable GetUserManagementDetails(int userId)
        {
            ISelectionFactory<UserProfileDetails> selectionFactory = new GetUsersSelectionFactory();
            UserProfileDetails userProfileDetails = new UserProfileDetails() { CurrentUserId = userId };
            
            return Find(selectionFactory, userProfileDetails);
        }

        public bool UpdateUserProfile(UserProfileDetails userProfileDetails)
        {
            IUpdateFactory<UserProfileDetails> updateFactory = new EditUserUpdateFactory();
            DbCommand outVal;
            int retVal = -1;
            bool isConverted = false;

            Save(updateFactory, userProfileDetails, out outVal);

            var statusOutValParm = outVal != null && outVal.Parameters != null && outVal.Parameters.Contains("@Status") ? outVal.Parameters["@Status"].Value : null;

            if (statusOutValParm != null)
            {
                isConverted = Int32.TryParse(statusOutValParm.ToString(), out retVal);
            }

            return isConverted && retVal == 0;
        }

        public void UpdateSAPAccount(UserProfileDetails userProfileDetails)
        {
            IUpdateFactory<UserProfileDetails> updateFactory = new SAPAccountUpdateFactory();
            DbCommand outVal;
            int retVal = -1;
            bool isConverted = false;

            Save(updateFactory, userProfileDetails);            
        }

        

        public bool CreateUser(UserProfileDetails userProfileDetails, out int newUserId)
        {
            IUpdateFactory<UserProfileDetails> updateFactory = new CreateUserUpdateFactory();
            DbCommand outVal;
            int retVal = -1;
            bool isConverted = false;

            Save(updateFactory, userProfileDetails, out outVal);

            var userIdOutValParm = outVal != null && outVal.Parameters != null && outVal.Parameters.Contains("@NewUserid") ? outVal.Parameters["@NewUserid"].Value : null;
            var statusOutValParm = outVal != null && outVal.Parameters != null && outVal.Parameters.Contains("@Status") ? outVal.Parameters["@Status"].Value : null;
       
            newUserId = 0;

            if (userIdOutValParm != null && statusOutValParm != null)
            {
                isConverted = Int32.TryParse(userIdOutValParm.ToString(), out newUserId);
                isConverted = isConverted && Int32.TryParse(statusOutValParm.ToString(), out retVal);
            }

            return isConverted && retVal == 0;
        }

        public bool DisableUser(string currentUserName, int userId)
        {
            IUpdateFactory<UserProfileDetails> updateFactory = new DisableUserUpdateFactory();
            UserProfileDetails userProfileDetails = new UserProfileDetails() { UserInfo = new UserInfo() { UserId = userId }, CurrentUserName = currentUserName };
            DbCommand outVal;
            int retVal = -1;
            bool isConverted = false;
            Save(updateFactory, userProfileDetails, out outVal);

            var statusOutValParm = outVal != null && outVal.Parameters != null && outVal.Parameters.Contains("@Status") ? outVal.Parameters["@Status"].Value : null;

            if (statusOutValParm != null)
            {
                isConverted = Int32.TryParse(statusOutValParm.ToString(), out retVal);
            }

            return isConverted && retVal == 0;
        }
        #endregion
    }
}