﻿using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;
using System.Data;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class GetAssignableRolesSelectionFactory : ISelectionFactory<UserProfileDetails>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (db != null && userProfileDetails != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAssignableRoles);
                db.AddInParameter(command, "@UserId", DbType.Int32, userProfileDetails.CurrentUserId);
            }
            return command;
        }
        #endregion
    }
}