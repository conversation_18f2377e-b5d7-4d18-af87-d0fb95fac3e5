﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class DisableUserUpdateFactory : IUpdateFactory<UserProfileDetails>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (db != null && userProfileDetails != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_DisableUser);
                db.AddInParameter(command, "@CurrentUserName", DbType.String, userProfileDetails.CurrentUserName);
                db.AddInParameter(command, "@UserId", DbType.Int32, userProfileDetails.UserInfo.UserId);
                db.AddInParameter(command, "@UpdateDateTimeOffset", DbType.DateTimeOffset, new DateTimeOffset(DateTime.Now));
                db.AddOutParameter(command, "@status", DbType.String, 2);
            }
            return command;
        }
        #endregion
    }
}