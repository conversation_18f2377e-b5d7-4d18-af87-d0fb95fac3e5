﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class CreateUserUpdateFactory : IUpdateFactory<UserProfileDetails>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (db != null && userProfileDetails != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateUserProfile);
                db.AddInParameter(command, "@UserName", DbType.String, userProfileDetails.UserInfo.UserName);
                db.AddInParameter(command, "@FirstName", DbType.String, userProfileDetails.UserInfo.FirstName);
                db.AddInParameter(command, "@LastName", DbType.String, userProfileDetails.UserInfo.LastName);
                db.AddInParameter(command, "@RoleName", DbType.String, userProfileDetails.UserInfo.RoleName);
                db.AddInParameter(command, "@Language", DbType.String, userProfileDetails.UserInfo.Language);
                db.AddInParameter(command, "@Terminal", DbType.String, userProfileDetails.Terminals.ToString());
                db.AddInParameter(command, "@UserId", DbType.Int32, 0);
                db.AddInParameter(command, "@SAPUserID", DbType.String, userProfileDetails.UserInfo.SapUserId);
                db.AddInParameter(command, "@UpdateDateTime", DbType.DateTime, DateTime.Now);
                db.AddInParameter(command, "@CurrentUserName", DbType.String, userProfileDetails.CurrentUserName);
                db.AddOutParameter(command, "@NewUserid", DbType.Int32, 0);
                db.AddInParameter(command, "@Email", DbType.String, userProfileDetails.UserInfo.Email);
                db.AddOutParameter(command, "@status", DbType.String, 2);
            }
            return command;
        }
        #endregion Public Methods
    }
}