﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class GetUsersSelectionFactory : ISelectionFactory<UserProfileDetails>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (null!= db && null != userProfileDetails)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetUsers);
                db.AddInParameter(command, "@UserID", DbType.Int32, userProfileDetails.CurrentUserId);
            }
            return command;
        }
        #endregion
    }
}