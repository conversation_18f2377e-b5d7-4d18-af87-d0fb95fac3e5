﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserManagement
{
    public class SAPAccountUpdateFactory : IUpdateFactory<UserProfileDetails>
    {
        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (db != null && userProfileDetails != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateSAPAccount);
                db.AddInParameter(command, "@Userid", DbType.Int32, userProfileDetails.UserInfo.UserId);
                db.AddInParameter(command, "@SAPUserID", DbType.String, userProfileDetails.UserInfo.SapUserId);                                
            }
            return command;
        }

        #endregion Public Methods
    }
}