﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class LoadBalanceRestoreSelectionFactory : ISelectionFactory<ShiftManagementIdentity>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, ShiftManagementIdentity identity)
        {
            DbCommand command = null;
            if (db != null && identity != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_LoadBalanceReset);
                db.AddInParameter(command, "@Terminal", DbType.String, identity.TerminalName);
                db.AddInParameter(command, "@UserName", DbType.String, identity.UserName);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 1);
            }
            return command;
        }
        #endregion
    }
}