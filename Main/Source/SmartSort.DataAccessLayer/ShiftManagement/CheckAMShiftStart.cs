﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class CheckAMShiftStart : ISelectionFactory<ShiftManagementIdentity>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, ShiftManagementIdentity identity)
        {
            DbCommand command = null;
            if (db != null && identity != null && !String.IsNullOrWhiteSpace(identity.TerminalName))
            {
                command = db.GetStoredProcCommand(Constants.SP_CheckAMShift);
                db.AddInParameter(command, "@CallStack", DbType.String, null);
                db.AddInParameter(command, "@Terminal", DbType.String, identity.TerminalName);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 5);
                db.AddOutParameter(command, "@PRCount", DbType.Int32, Int32.MaxValue);
            }
            return command;
        }
        #endregion
    }
}
