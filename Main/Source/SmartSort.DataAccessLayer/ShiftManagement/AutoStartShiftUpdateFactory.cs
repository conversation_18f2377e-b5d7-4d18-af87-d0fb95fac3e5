﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
    public class AutoStartShiftUpdateFactory : IUpdateFactory<AutoStartShift>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, AutoStartShift autoStart)
        {
            DbCommand command = null;
            if (db != null && autoStart != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_SetAutoStartShiftTime);
                db.AddInParameter(command, "@Terminal", DbType.String, autoStart.Terminal);
                db.AddInParameter(command, "@ShiftAutoStartTimeChar", DbType.String, autoStart.AutoStartTime);                                
            }
            return command;
        }
        #endregion
    }
}