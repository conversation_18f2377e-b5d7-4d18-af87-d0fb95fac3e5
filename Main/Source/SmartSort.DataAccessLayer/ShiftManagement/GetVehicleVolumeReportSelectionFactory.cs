﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetVehicleVolumeReportSelectionFactory : ISelectionFactory<VehicleVolumeIdentity>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, VehicleVolumeIdentity identity)
        {
            DbCommand command = null;
            if (db != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetVehicleVolumeReport);
                db.AddInParameter(command, "@Terminal", DbType.String, identity.TerminalName);
                db.AddInParameter(command, "@FreezeFlag", DbType.Boolean, identity.Frozen);                
                db.AddOutParameter(command, "@WorkingDate", DbType.Date, 15);
                command.CommandTimeout = 120;
            }
            return command;
        }
        #endregion
    }
}