﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
    public class GetAddressesByRouteShelfSelectionFactory : ISelectionFactory<PreShiftLoadBalancing>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, PreShiftLoadBalancing preShiftLoadBalancing)
        {
            DbCommand command = null;
            if (db != null && preShiftLoadBalancing != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAddressesByRouteShelf);
                db.AddInParameter(command, "@Terminal", DbType.String, preShiftLoadBalancing.TerminalName);
                db.AddInParameter(command, "@Route", DbType.String, preShiftLoadBalancing.SourceRoute);
                db.AddInParameter(command, "@Shelf", DbType.String, preShiftLoadBalancing.SourceShelf);
            }
            return command;
        }
        #endregion
    }
}