﻿using SmartSort.Business.Entities;
using System;
using System.Collections.Generic;

namespace SmartSort.DataAccessLayer
{
    //public class PreShiftLoadBalancingIdentity
    //{
    //    #region Public Properties
    //    public string TerminalName { get; set; }
    //    public string SourceRoute { get; set; }
    //    public string SourceShelf { get; set; }
    //    public string TargetRoute { get; set; }
    //    public string TargetShelf { get; set; }
    //    public List<CustomerStop> CustomerStops { get; set; }
    //    public string UserName { get; set; }
    //    #endregion
    //}
}