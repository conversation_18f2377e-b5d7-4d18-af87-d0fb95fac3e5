﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetAutoStartShiftSelectionFactory : ISelectionFactory<string>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, string terminalName)
        {
            DbCommand command = null;
            if (db != null && !String.IsNullOrWhiteSpace(terminalName))
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAutoStartShiftTime);                
                db.AddInParameter(command, "@Terminal", DbType.String, terminalName);                
            }
            return command;
        }
        #endregion
    }
}