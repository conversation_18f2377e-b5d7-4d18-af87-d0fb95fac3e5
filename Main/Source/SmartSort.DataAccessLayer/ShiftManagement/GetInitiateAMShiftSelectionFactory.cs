﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetStartAMShiftSelectionFactory : ISelectionFactory<ShiftManagementIdentity>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, ShiftManagementIdentity identity)
        {
            DbCommand command = null;
            if (db != null && identity != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_InitiateAMShift);
                db.AddInParameter(command, "@Terminal", DbType.String, identity.TerminalName);
                db.AddInParameter(command, "@Date", DbType.DateTimeOffset, identity.DateTimeOffset);
                db.AddInParameter(command, "@CurrentUser", DbType.String, identity.UserName);
                db.AddInParameter(command, "@ShiftDate", DbType.DateTime, identity.DateTime);
                db.AddOutParameter(command, "@Success", DbType.Boolean, 1);
            }
            return command;
        }
        #endregion
    }
}