﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetMoveHistorySelectionFactory : ISelectionFactory<MoveHistoryIdentity>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, MoveHistoryIdentity identity)
        {
            DbCommand command = null;
            if (db != null && identity != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetMoveHistory);
                db.AddInParameter(command, "@Terminal", DbType.String, identity.Terminal);
                db.AddInParameter(command, "@WorkingDate", DbType.Date, identity.Date);
                command.CommandTimeout = 120;
            }
            return command;
        }
        #endregion
    }
}