﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
    public class ApplyPreviousMoveUpdateFactory : IUpdateFactory<ApplyPreviousMove>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, ApplyPreviousMove move)
        {
            DbCommand command = null;
            if (db != null && move != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_ApplyPreviousMoveUpdate);
                db.AddInParameter(command, "@Terminal", DbType.String, move.Terminal);
                db.AddInParameter(command, "@SelectedDate", DbType.Date, move.SelectedDate);
                db.AddInParameter(command, "@UserName", DbType.String, move.UserName);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 5);
            }
            return command;
        }
        #endregion
    }
}