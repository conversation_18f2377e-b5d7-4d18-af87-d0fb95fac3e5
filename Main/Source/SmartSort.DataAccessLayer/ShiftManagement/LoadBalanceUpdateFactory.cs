﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
    public class LoadBalanceUpdateFactory : IUpdateFactory<PreShiftLoadBalancing>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, PreShiftLoadBalancing preShiftLoadBalancing)
        {
            DbCommand command = null;
            if (db != null && preShiftLoadBalancing != null)
            {
                string customerStopsXml = preShiftLoadBalancing.CustomerStops == null || preShiftLoadBalancing.CustomerStops.Count == 0 || preShiftLoadBalancing.IsMoveEntireShelf ? String.Empty :
                    TransformEntity.ObjectToXML(preShiftLoadBalancing.CustomerStops);

                command = db.GetStoredProcCommand(Constants.SP_LoadBalanceMoveAddr);
                db.AddInParameter(command, "@Terminal", DbType.String, preShiftLoadBalancing.TerminalName);
                db.AddInParameter(command, "@FromRoute", DbType.String, preShiftLoadBalancing.SourceRoute);
                db.AddInParameter(command, "@FromShelf", DbType.String, preShiftLoadBalancing.SourceShelf);
                db.AddInParameter(command, "@ToRoute", DbType.String, preShiftLoadBalancing.TargetRoute);
                db.AddInParameter(command, "@ToShelf", DbType.String, preShiftLoadBalancing.TargetShelf);
                db.AddInParameter(command, "@AddressList", DbType.Xml, customerStopsXml);
                db.AddInParameter(command, "@UserName", DbType.String, preShiftLoadBalancing.UserName);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 1);
            }
            return command;
        }
        #endregion
    }
}