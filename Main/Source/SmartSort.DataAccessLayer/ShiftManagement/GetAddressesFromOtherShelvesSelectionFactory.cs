﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
    public class GetAddressesFromOtherShelvesSelectionFactory : ISelectionFactory<PreShiftLoadBalancing>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, PreShiftLoadBalancing preShiftLoadBalancing)
        {
            DbCommand command = null;
            if (db != null && preShiftLoadBalancing != null)
            {
                string customerStopsXml = string.Empty;
                if (preShiftLoadBalancing.CustomerStops.Count > 0)
                {
                    customerStopsXml = TransformEntity.ObjectToXML(preShiftLoadBalancing.CustomerStops);
                }                

                command = db.GetStoredProcCommand(Constants.SP_GetAddressesFromOtherShelves);
                db.AddInParameter(command, "@Terminal", DbType.String, preShiftLoadBalancing.TerminalName);
                db.AddInParameter(command, "@FromRoute", DbType.String, preShiftLoadBalancing.SourceRoute);
                db.AddInParameter(command, "@FromShelf", DbType.String, preShiftLoadBalancing.SourceShelf);
                db.AddInParameter(command, "@ToRoute", DbType.String, preShiftLoadBalancing.TargetRoute);
                db.AddInParameter(command, "@ToShelf", DbType.String, preShiftLoadBalancing.TargetShelf);
                db.AddInParameter(command, "@Addresses", DbType.Xml, customerStopsXml);
                
            }
            return command;
        }
        #endregion
    }
}