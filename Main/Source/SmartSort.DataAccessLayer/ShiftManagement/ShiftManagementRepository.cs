﻿using System;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Diagnostics;
using SmartSort.Business.Entities;
using SmartSort.Common;
using System.Collections.Generic;

namespace SmartSort.DataAccessLayer
{
    public class ShiftManagementRepository : Repository<PreShiftLoadBalancing>
    {
        #region Public Constructors

        public ShiftManagementRepository(string databaseName)
            : base(databaseName)
        {
        }

        #endregion Public Constructors

        #region Public Methods
        public DataTableCollection GetVehicleVolumeReport(string terminalName, bool frozen, out DateTime shiftDate)
        {
            DbCommand outCommand = null;
            ISelectionFactory<VehicleVolumeIdentity> selectionFactory = new GetVehicleVolumeReportSelectionFactory();
            VehicleVolumeIdentity identity = new VehicleVolumeIdentity();
            identity.TerminalName = terminalName;
            identity.Frozen = frozen;
            DataTableCollection dataTableCollection = base.FindAll(selectionFactory, identity, out outCommand);
            shiftDate = Convert.ToDateTime(outCommand.Parameters["@WorkingDate"].Value);
            return dataTableCollection;
        }

        public DataTableCollection GetRoutesAndShelves(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetRouteShelfByTerminalSelectionFactory();
            DataTableCollection dataTableCollection = base.FindAll(selectFactory, terminalName);
            return dataTableCollection;
        }

        public DataTable GetShiftStatisticsDetails(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetShiftStatisticsDetailsSelectionFactory();
            DataTable dataTable = base.Find(selectFactory, terminalName);
            return dataTable;
        }

        public DataTable GetMoveHistory(string terminalName, DateTime date)
        {
            ISelectionFactory<MoveHistoryIdentity> selectFactory = new GetMoveHistorySelectionFactory();
            MoveHistoryIdentity identity = new MoveHistoryIdentity();            
            identity.Terminal = terminalName;
            identity.Date = date;            
            DataTable dataTable = base.Find(selectFactory, identity);
            return dataTable;
        }

        public DataTable GetAddressesByRouteShelf(PreShiftLoadBalancing preShifLoadBalancing)
        {
            ISelectionFactory<PreShiftLoadBalancing> selectFactory = new GetAddressesByRouteShelfSelectionFactory();
            DataTable dataTable = base.Find(selectFactory, preShifLoadBalancing);
            return dataTable;
        }


        public DataTable GetActualsAddressesByRouteShelf(PreShiftLoadBalancing preShifLoadBalancing)
        {
            ISelectionFactory<PreShiftLoadBalancing> selectFactory = new GetActualsAddressesByRouteShelfSelectionFactory();
            DataTable dataTable = base.Find(selectFactory, preShifLoadBalancing);
            return dataTable;
        }

        public DataTable GetAddressesFromOtherShelves(PreShiftLoadBalancing preShifLoadBalancing)
        { 
            ISelectionFactory<PreShiftLoadBalancing> selectFactory = new GetAddressesFromOtherShelvesSelectionFactory();
            DataTable dataTable = base.Find(selectFactory, preShifLoadBalancing);
            return dataTable;
        }

        public bool TransferLoad(PreShiftLoadBalancing preShifLoadBalancing)
        {
            DbCommand outCommand = null;
            IUpdateFactory<PreShiftLoadBalancing> updateFactory = new LoadBalanceUpdateFactory();
            bool isSuccess = Save(updateFactory, preShifLoadBalancing, out outCommand);
            isSuccess = isSuccess && outCommand != null ? Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 : false;
            return isSuccess;
        }

        public bool UndoLoadBalanceMoves(string terminalName, string userName)
        {
            DbCommand outCommand = null;
            ISelectionFactory<ShiftManagementIdentity> selectFactory = new LoadBalanceRestoreSelectionFactory();
            ShiftManagementIdentity identity = new ShiftManagementIdentity()
            {
                TerminalName = terminalName,
                UserName = userName,
            };

            base.Find(selectFactory, identity, out outCommand);
            bool isRestored = outCommand != null ? Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 : false;
            return isRestored;
        }

        public bool InitiateAMShift(string terminalName, DateTimeOffset dateTimeOffset, string userName, DateTime date)
        {
            DbCommand outCommand = null;
            ISelectionFactory<ShiftManagementIdentity> selectFactory = new GetStartAMShiftSelectionFactory();
            ShiftManagementIdentity identity = new ShiftManagementIdentity()
            {
                TerminalName = terminalName,
                DateTimeOffset = dateTimeOffset,
                UserName = userName,
                DateTime = date
            };
            base.Find(selectFactory, identity, out outCommand);
            bool isShiftStarted = outCommand != null ? Convert.ToInt32(outCommand.Parameters["@Success"].Value) == 0 : false;
            return isShiftStarted;
        }

        public string GetActiveSortPlan(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetActiveSortPlanSelectionFactory();
            string activePlan = string.Empty;
            DataTable dataTable = base.Find(selectFactory, terminalName); 
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                DataRow row = dataTable.Rows[0];
                activePlan = row[Constants.SortPlanCodeTableColumnName] as string;
            } 
            return activePlan;
        }

        public string GetWorkingDay(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetWorkingDaySelectionFactory();
            string workingDay = string.Empty;
            DataTable dataTable = base.Find(selectFactory, terminalName);
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                DataRow row = dataTable.Rows[0];
                workingDay = row[0] as string;
            }
            return workingDay;
        }

        public string GetAutoStartTime(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetAutoStartShiftSelectionFactory();
            string autoStartTime = string.Empty;
            DataTable dataTable = base.Find(selectFactory, terminalName);
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                DataRow row = dataTable.Rows[0];
                autoStartTime = row[0] as string;
            }
            return autoStartTime;
        }

        public List<MoveDateHistory> GetPreviousMoveDates(string terminalName)
        {
            ISelectionFactory<string> selectFactory = new GetPreviousMovesHistorySelectionFactory();
            DataTable dataTable = base.Find(selectFactory, terminalName);
            return dataTable.DataTableToList<MoveDateHistory>(false, false); ;
        }

        public bool SetAutoStartTime(string terminal, string autoStartTime)
        {            
            AutoStartShift input = new AutoStartShift()
            {
                Terminal = terminal,
                AutoStartTime = autoStartTime
            };            
            IUpdateFactory<AutoStartShift> updateFactory = new AutoStartShiftUpdateFactory();
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, input))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command));
            }          
        }

        public bool ApplyPrevMove(string terminal, DateTime date, string user)
        {
            ApplyPreviousMove input = new ApplyPreviousMove()
            {
                Terminal = terminal,
                SelectedDate = date,
                UserName = user
            };
            IUpdateFactory<ApplyPreviousMove> updateFactory = new ApplyPreviousMoveUpdateFactory();
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, input))
            {
                db.ExecuteNonQuery(command);
                return Convert.ToInt32(command.Parameters["@Status"].Value) == 0;
            }          
        }

        
        public ShiftStatus IsShiftStarted(string terminalName)
        {
            DbCommand outCommand = null;
            ISelectionFactory<ShiftManagementIdentity> selectFactory = new CheckAMShiftStart();
            ShiftManagementIdentity identity = new ShiftManagementIdentity()
            {
                TerminalName = terminalName,
            };
            DataTable dataTable = base.Find(selectFactory, identity, out outCommand);
            bool isStartShift = Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0;
            int resetPinCount = Convert.ToInt32(outCommand.Parameters["@PRCount"].Value);
            return new ShiftStatus { IsShiftStarted = isStartShift, ResetPinCount= resetPinCount };
        }
        #endregion
    }
}