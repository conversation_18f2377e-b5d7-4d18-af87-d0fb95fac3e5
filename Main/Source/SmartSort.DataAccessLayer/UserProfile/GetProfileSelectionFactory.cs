﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserProfile
{
    /// <summary>
    /// Selection factory for User Profile.
    /// </summary>
    internal class GetProfileSelectionFactory : ISelectionFactory<UserProfileDetails>
    {
        public DbCommand ConstructSelectCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (null != db && null != userProfileDetails)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetProfileInfo);
                db.AddInParameter(command, "@UserId", DbType.Int32, userProfileDetails.UserInfo.UserId);
            }
            return command;
        }
    }
}