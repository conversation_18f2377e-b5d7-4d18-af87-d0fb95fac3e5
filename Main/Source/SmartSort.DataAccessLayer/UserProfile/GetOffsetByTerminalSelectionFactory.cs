﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserProfile
{
    /// <summary>
    /// Selection factory for User Profile.
    /// </summary>
    internal class GetOffsetByTerminalSelectionFactory : ISelectionFactory<string>
    {
        public DbCommand ConstructSelectCommand(Database db, string terminal)
        {
            DbCommand command = null;
            if (db != null && !String.IsNullOrWhiteSpace(terminal))
            {
                command = db.GetStoredProcCommand(Constants.SP_GetOffsetByTerminal);
                db.AddInParameter(command, "@Terminal", DbType.String, terminal);
            }
            return command;
        }
    }
}