﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserProfile
{
    class GetProfileBySAPSelectionFactory : ISelectionFactory<UserProfileDetails>
    {
        public DbCommand ConstructSelectCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (null != db && null != userProfileDetails)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetProfileInfoBySAP);
                db.AddInParameter(command, "@SAPUserID", DbType.String, userProfileDetails.UserInfo.SapUserId);
                db.AddOutParameter(command, "@status", DbType.Int32, 2);
            }
            return command;
        }
    }
}
