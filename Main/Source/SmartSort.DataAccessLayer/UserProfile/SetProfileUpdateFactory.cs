﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.UserProfile
{
    /// <summary>
    /// Update factory for User Profile.
    /// </summary>
    internal class SetProfileUpdateFactory : IUpdateFactory<UserProfileDetails>
    {
        public DbCommand ConstructUpdateCommand(Database db, UserProfileDetails userProfileDetails)
        {
            DbCommand command = null;
            if (null != db && null != userProfileDetails)
            {
                command = db.GetStoredProcCommand(Constants.SP_SetProfileInfo);
                db.AddInParameter(command, "@UserId", DbType.Int32, userProfileDetails.UserInfo.UserId);
                db.AddInParameter(command, "@SAPUserID", DbType.String, userProfileDetails.UserInfo.SapUserId);
                db.AddInParameter(command, "@FirstName", DbType.String, userProfileDetails.UserInfo.FirstName);
                db.AddInParameter(command, "@LastName", DbType.String, userProfileDetails.UserInfo.LastName);
                db.AddInParameter(command, "@Language", DbType.String, userProfileDetails.UserInfo.Language);
                db.AddInParameter(command, "@ReceiveEmailNotIf", DbType.String, userProfileDetails.UserInfo.ReceiveEmailNotIf);
                db.AddInParameter(command, "@Email", DbType.String, userProfileDetails.UserInfo.Email);
                db.AddOutParameter(command, "@Status", DbType.String, 10);
            }
            return command;
        }
    }
}