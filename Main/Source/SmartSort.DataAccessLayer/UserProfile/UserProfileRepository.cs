﻿using System;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer.UserProfile
{
    public class UserProfileRepository : Repository<UserProfileDetails>
    {
        private DbCommand outValue;

        public UserProfileRepository(string databaseName)
            : base(databaseName)
        {
        }

        /// <summary>
        /// Getting User Profile Info by UserId.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public DataTable GetProfileInfo(int userId)
        {
            ISelectionFactory<UserProfileDetails> selectionFactory = new GetProfileSelectionFactory();
            //Boolean t = Convert.ToBoolean(2);
            UserProfileDetails userProfileDetails = new UserProfileDetails();
            UserInfo userInfo = new UserInfo();
            userProfileDetails.UserInfo = userInfo;
            userProfileDetails.UserInfo.UserId = userId;
            return Find(selectionFactory, userProfileDetails);
        }


        /// <summary>
        /// Getting User Profile Info by SAPUserName.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public DataTable GetProfileInfobySAP(string SAPUserName)
        {
            ISelectionFactory<UserProfileDetails> selectionFactory = new GetProfileBySAPSelectionFactory();            
            UserProfileDetails userProfileDetails = new UserProfileDetails();
            UserInfo userInfo = new UserInfo();
            userProfileDetails.UserInfo = userInfo;
            userProfileDetails.UserInfo.SapUserId = SAPUserName;
            return Find(selectionFactory, userProfileDetails);
        }

        /// <summary>
        /// Setting User Profile Info in database.
        /// </summary>
        /// <param name="userProfileDetails"></param>
        /// <returns></returns>
        public void SetProfileInfo(UserProfileDetails userProfileDetails, out bool status)
        {
            IUpdateFactory<UserProfileDetails> updateFactory = new SetProfileUpdateFactory();
            Save(updateFactory, userProfileDetails, out outValue);
            status = Convert.ToInt32(outValue.Parameters["@Status"].Value) == 0 ? true : false;
        }


        public DataTable GetOffsetByTerminal(string terminal)
        {
            ISelectionFactory<string> selectionFactory = new GetOffsetByTerminalSelectionFactory();
            return Find(selectionFactory, terminal);
        }
    }
}