﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetActiveSortPlansForTerminalSelectionRepository : ISelectionFactory<SortPlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, SortPlanIdentity sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetActiveSortPlan);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@InDate", DbType.DateTime, Convert.ToDateTime(sortPlan.Date).ToShortDateString());
            }
            return command;
        }
    }
}