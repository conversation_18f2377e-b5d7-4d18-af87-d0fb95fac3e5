﻿using Microsoft.Practices.EnterpriseLibrary.Data;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.DataAccessLayer
{
    public class GetMaxEndDateSelectionFactory : ISelectionFactory<SortPlanIdentity>
    {
       public DbCommand ConstructSelectCommand(Database db, SortPlanIdentity sortPlan)
       {
           DbCommand command = null;
           if (null != db && null != sortPlan)
           {
               command = db.GetStoredProcCommand("dbo.CheckFiscalCalendar");
           }
           return command;
       }
    }
}
