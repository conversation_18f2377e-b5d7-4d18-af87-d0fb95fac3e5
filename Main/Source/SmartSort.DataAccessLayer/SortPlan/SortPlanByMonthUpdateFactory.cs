﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    internal class SortPlanByMonthUpdateFactory : IUpdateFactory<RouteSortPlan>
    {
        public DbCommand ConstructUpdateCommand(Database db, RouteSortPlan sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_SetActiveSortPlanForDate);
                if (sortPlan.SortPlanId == 0)
                    db.AddInParameter(command, "@SortPlanId", DbType.Int32, DBNull.Value);
                else
                    db.AddInParameter(command, "@SortPlanId", DbType.Int32, sortPlan.SortPlanId);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.TerminalName);
                db.AddInParameter(command, "@InDate", DbType.DateTime, Convert.ToDateTime(sortPlan.DateString).ToShortDateString());
                db.AddInParameter(command, "@userName", DbType.String, sortPlan.UserName);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 10);
            }
            return command;
        }
    }
}