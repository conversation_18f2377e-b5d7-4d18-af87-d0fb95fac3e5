﻿using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.DataAccessLayer
{
    class RemoveSortPlanRecurrenceDeleteFactory : IDeleteFactory<SortPlanIdentity>
    {
        public DbCommand ConstructDeleteCommand(Database db, SortPlanIdentity sortPlanIdentity)
        {
            DbCommand command = null;
            if (null != db && null != sortPlanIdentity)
            {
                command = db.GetStoredProcCommand(Constants.SP_DeleteSortPlanSchedule);
                db.AddInParameter(command, "@SortPlanScheduleId", DbType.Int32, sortPlanIdentity.SortPlanScheduleId);
                db.AddInParameter(command, "@UserName", DbType.String, sortPlanIdentity.UserName);
            }
            return command;
        }
    }
}
