﻿namespace SmartSort.DataAccessLayer
{
    public class SortPlanIdentity
    {
        
        public string Date { get; set; }

        public string Terminal { get; set; }

        public int Month { get; set; }

        public int Year { get; set; }

        public string SortPlanId { get; set; }

        public string UserName { get; set; }

        public int SortPlanScheduleId { get; set; }

        public string StartDate { get; set; }

        public string EndDate { get; set; }


        public SortPlanIdentity(string terminal, string startDate, string endDate)
        {
            Terminal = terminal;
            StartDate = startDate;
            EndDate = endDate;
        }
        public SortPlanIdentity(int sortPlanScheduleId,string userName)
        {
            SortPlanScheduleId = sortPlanScheduleId;
            UserName = userName;
        }
        public SortPlanIdentity(string terminal, string date)
        {
            Terminal = terminal;
            Date = date;
        }

        public SortPlanIdentity(string terminal, string date, string sortPlanId, string userName)
        {
            Terminal = terminal;
            Date = date;
            SortPlanId = sortPlanId;
            UserName = userName;
        }

        public SortPlanIdentity(int sortPlanScheduleId, string startDate, string endDate,string terminalName)
        {
            SortPlanScheduleId = sortPlanScheduleId;
            StartDate = startDate;
            EndDate = endDate;
            Terminal = terminalName;
        }

        public SortPlanIdentity()
        {
            // TODO: Complete member initialization
        }
    }
}