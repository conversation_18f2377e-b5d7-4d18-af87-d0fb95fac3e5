﻿using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.DataAccessLayer
{
    public class GetSortPlanScheduleDatesSelectionFactory : ISelectionFactory<SortPlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, SortPlanIdentity sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetSortPlanScheduleDates);
                db.AddInParameter(command, "@SortPlanScheduleId", DbType.Int32, sortPlan.SortPlanScheduleId);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@StartDate", DbType.DateTime, DateTime.ParseExact(sortPlan.StartDate, "dd/MM/yyyy", CultureInfo.InvariantCulture));
                db.AddInParameter(command, "@EndDate", DbType.DateTime, DateTime.ParseExact(sortPlan.EndDate, "dd/MM/yyyy", CultureInfo.InvariantCulture));
            }
            return command;
        }
    }
}