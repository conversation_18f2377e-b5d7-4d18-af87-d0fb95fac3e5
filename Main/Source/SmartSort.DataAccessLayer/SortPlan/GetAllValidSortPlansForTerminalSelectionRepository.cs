﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace SmartSort.DataAccessLayer
{
    public class GetAllValidSortPlansForTerminalSelectionRepository : ISelectionFactory<SortPlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, SortPlanIdentity sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand("dbo.GetAllValidSortPlans");
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
            }
            return command;
        }
    }
}