﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class SortPlanRepository : Repository<RouteSortPlan>
    {
        private DbCommand outValue;
        private bool saveStatus;

        public SortPlanRepository(string databaseName)
            : base(databaseName)
        {
        }

        /// <summary>
        /// Get route plans for terminal
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="startDate">Month</param>
        /// <param name="endDate">Year</param>
        /// <returns>Data Table with sort plans</returns>
        public List<RouteSortPlan> GetSortPlanForCalendar(string terminal, string startDate, string endDate)
        {
            ISelectionFactory<SortPlanIdentity> selectionFactory = new GetSortPlanForCalendarSelectionFactory();
            SortPlanIdentity sortPlanIdentity = new SortPlanIdentity(terminal, startDate, endDate);
            DataTable dtSortPlan = Find(selectionFactory, sortPlanIdentity);
            return dtSortPlan.DataTableToList<RouteSortPlan>();
        }

        /// <summary>
        /// Get Active Sort plans for terminal
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="date">Date</param>
        /// <param name="activeSortPlans">List of Active sort plans</param>
        /// <returns>List of Sort plans with active sort plans for terminal</returns>
        public List<RouteSortPlan> GetActiveSortPlansForTerminal(string terminal, string date, out List<RouteSortPlan> activeSortPlans)
        {
            ISelectionFactory<SortPlanIdentity> selectionFactory = new GetActiveSortPlansForTerminalSelectionRepository();
            ISelectionFactory<SortPlanIdentity> selectionFactoryForValidSortPlans = new GetAllValidSortPlansForTerminalSelectionRepository();
            SortPlanIdentity sortPlanIdentity = new SortPlanIdentity(terminal, date);
            DataTable dtSortPlan = Find(selectionFactory, sortPlanIdentity);
            DataTable dtRouteSortPlan = Find(selectionFactoryForValidSortPlans, sortPlanIdentity);
            activeSortPlans = dtRouteSortPlan.DataTableToList<RouteSortPlan>();
            return dtSortPlan.DataTableToList<RouteSortPlan>();
        }

        /// <summary>
        /// Update Active Sort plans for terminal
        /// </summary>
        /// <param name="sortPlan">Sort Plan Entity</param>
        /// <returns>True or False</returns>
        public bool UpdateSortPlanForDate(RouteSortPlan sortPlan)
        {
            IUpdateFactory<RouteSortPlan> updateFactory = new SortPlanByMonthUpdateFactory();
            Save(updateFactory, sortPlan, out outValue);
            return saveStatus = Convert.ToInt32(outValue.Parameters["@Status"].Value) == 0 ? true : false;
        }

        /// <summary>
        /// Update Active Sort plans for recurrence
        /// </summary>
        /// <param name="sortPlan">Sort Plan Entity</param>
        /// <returns>True or False</returns>
        public bool UpdateSortPlanForRecurrence(RouteSortPlan sortPlan)
        {
            IUpdateFactory<RouteSortPlan> updateFactory = new SortPlanRecurrenceUpdateFactory();
            Save(updateFactory, sortPlan, out outValue);
            return saveStatus = Convert.ToInt32(outValue.Parameters["@DatesCount"].Value) == 0 ? false : true;
        }

        public bool RemoveSortPlanRecurrence(int sortPlanScheduleId,string userName)
        {
            SortPlanIdentity sortPlanIdentity = new SortPlanIdentity(sortPlanScheduleId,userName);
            IDeleteFactory<SortPlanIdentity> deleteFactory = new RemoveSortPlanRecurrenceDeleteFactory();
            return Remove(deleteFactory, sortPlanIdentity);
        }

        public List<RouteSortPlan> GetSortPlanScheduleDates(RouteSortPlan routeSortPlan)
        {
            ISelectionFactory<SortPlanIdentity> selectionFactory = new GetSortPlanScheduleDatesSelectionFactory();
            SortPlanIdentity sortPlanIdentity = new SortPlanIdentity(routeSortPlan.SortplanScheduleID, routeSortPlan.ScheduleStartDateString, routeSortPlan.ScheduleEndDateString,routeSortPlan.TerminalName);
            DataTable dataTable = Find(selectionFactory, sortPlanIdentity);
            return dataTable.DataTableToList<RouteSortPlan>();
        }

        public DateTime GetMaxEndDate()
        {
            ISelectionFactory<SortPlanIdentity> selectionFactory = new GetMaxEndDateSelectionFactory();
            SortPlanIdentity sortPlanIdentity = new SortPlanIdentity();
            DataTable dataTable = Find(selectionFactory, sortPlanIdentity);
            return Convert.ToDateTime(dataTable.Rows[0][0]);
        }
    }
}