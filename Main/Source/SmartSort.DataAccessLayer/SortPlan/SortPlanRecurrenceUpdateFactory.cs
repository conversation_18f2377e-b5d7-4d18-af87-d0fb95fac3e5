﻿using System;
using System.Data;
using System.Data.Common;
using System.Globalization;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    class SortPlanRecurrenceUpdateFactory : IUpdateFactory<RouteSortPlan>
    {
        public DbCommand ConstructUpdateCommand(Database db, RouteSortPlan sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateSortPlanSchedule);
                db.AddInParameter(command, "@SortPlanScheduleId", DbType.Int32, sortPlan.SortplanScheduleID);
                db.AddInParameter(command, "@SortPlanId", DbType.Int32, sortPlan.SortPlanId);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.TerminalName);
                db.AddInParameter(command, "@ScheduleType", DbType.String, sortPlan.ScheduleType);
                db.AddInParameter(command, "@StartDate", DbType.DateTime, DateTime.ParseExact(sortPlan.ScheduleStartDateString, "dd/MM/yyyy", CultureInfo.InvariantCulture));
                db.AddInParameter(command, "@EndDate", DbType.DateTime, DateTime.ParseExact(sortPlan.ScheduleEndDateString,"dd/MM/yyyy", CultureInfo.InvariantCulture));
                db.AddInParameter(command, "@ParamType", DbType.Int32, sortPlan.ScheduleParamType);
                db.AddInParameter(command, "@Param1", DbType.String, sortPlan.ScheduleParam1);
                db.AddInParameter(command, "@Param2", DbType.String, sortPlan.ScheduleParam2);
                db.AddInParameter(command, "@userName", DbType.String, sortPlan.UserName);
                db.AddOutParameter(command, "@DatesCount", DbType.Int32, 10);
            }
            return command;
        }
    }
}
