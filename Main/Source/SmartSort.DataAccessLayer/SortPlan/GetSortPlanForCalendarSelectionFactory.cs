﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetSortPlanForCalendarSelectionFactory : ISelectionFactory<SortPlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, SortPlanIdentity sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetRoutePlansForCalendar);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@StartDate", DbType.DateTime, sortPlan.StartDate);
                db.AddInParameter(command, "@EndDate", DbType.DateTime, sortPlan.EndDate);
            }
            return command;
        }
    }
}