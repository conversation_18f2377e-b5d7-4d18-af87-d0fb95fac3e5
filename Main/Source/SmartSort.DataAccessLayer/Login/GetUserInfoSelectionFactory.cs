﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetUserInfoSelectionFactory : ISelectionFactory<LoginIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, LoginIdentity login)
        {
            DbCommand command = null;
            if (null != db && null != login)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetUserInfo);
                db.AddInParameter(command, "@UserName", DbType.String, login.UserId);
                db.AddOutParameter(command, "@status", DbType.String, 2);
                login.Status = Convert.ToString(db.GetParameterValue(command, "status"));
            }
            return command;
        }
    }
}