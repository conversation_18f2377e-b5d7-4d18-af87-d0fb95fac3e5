﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class ValidateTerminalSelectionFactory : ISelectionFactory<LoginIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, LoginIdentity login)
        {
            DbCommand command = null;
            if (null != db && null != login)
            {
                command = db.GetStoredProcCommand(Constants.SP_ValidateTerminals);
                db.AddInParameter(command, "@UserID", DbType.String, login.UserId);
                db.AddInParameter(command, "@terminal", DbType.String, login.Terminal);
                db.AddOutParameter(command, "@status", DbType.String, 2);
                login.Status = Convert.ToString(db.GetParameterValue(command, "status"));
            }
            return command;
        }
    }
}