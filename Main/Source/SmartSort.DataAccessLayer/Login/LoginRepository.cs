﻿using System;
using System.Data;
using System.Data.Common;

namespace SmartSort.DataAccessLayer
{
    public class LoginRepository : Repository<LoginIdentity>
    {
        private DataTable dataTable;
        private DbCommand outCommand;

        public LoginRepository(string databaseName)
            : base(databaseName)
        {
        }
        
        /// <summary>
        /// Terminal List
        /// </summary>
        /// <returns>Data Table With Terminal List</returns>
        public DataTable TerminalList
        {
            get
            {
                ISelectionFactory<LoginIdentity> selectionFactory = new GetTerminalListSelectionFactory();
                LoginIdentity loginIdentitys = new LoginIdentity(string.Empty, string.Empty, string.Empty, string.Empty, 0);
                dataTable = Find(selectionFactory, loginIdentitys);
                return dataTable;
            }
            set
            {
                dataTable = value;
            }
        }

        /// <summary>
        /// Validate Terminal for UserID
        /// </summary>
        /// <param name="userId">userId</param>
        /// <param name="terminal">terminal</param>
        /// <param name="status">status</param>
        /// <returns>Data Table With Terminal List </returns>
        public DataTable ValidateTerminal(string userId, string terminal, out string status)
        {
            ISelectionFactory<LoginIdentity> selectionFactory = new ValidateTerminalSelectionFactory();
            status = string.Empty;
            LoginIdentity loginIdentitys = new LoginIdentity(userId, terminal, status, string.Empty, 0);
            dataTable = Find(selectionFactory, loginIdentitys, out outCommand);
            status = Convert.ToString(outCommand.Parameters["@status"].Value);
            return dataTable;
        }

        /// <summary>
        /// Get User Information
        /// </summary>
        /// <param name="userName">userName</param>
        /// <param name="status">status</param>
        /// <returns>DataTable for UserInformation</returns>
        public DataTable GetUserInformation(string userName, out string status)
        {
            ISelectionFactory<LoginIdentity> selectionFactory = new GetUserInfoSelectionFactory();
            status = string.Empty;
            LoginIdentity loginIdentitys = new LoginIdentity(userName, string.Empty, status, userName, 0);
            dataTable = Find(selectionFactory, loginIdentitys, out outCommand);
            status = Convert.ToString(outCommand.Parameters["@status"].Value);
            return dataTable;
        }

        /// <summary>
        /// Get User roles
        /// </summary>
        /// <param name="userId">userID</param>
        /// <returns> DataTable for User Roles</returns>
        public DataTable GetUserRole(int userId)
        {
            ISelectionFactory<LoginIdentity> selectionFactory = new GetUserRolesSelectionFactory();
            LoginIdentity loginIdentitys = new LoginIdentity(Convert.ToString(userId), string.Empty, string.Empty, string.Empty, 0);
            dataTable = Find(selectionFactory, loginIdentitys);
            return dataTable;
        }
    }
}