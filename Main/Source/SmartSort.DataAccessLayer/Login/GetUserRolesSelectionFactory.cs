﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetUserRolesSelectionFactory : ISelectionFactory<LoginIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, LoginIdentity login)
        {
            DbCommand command = null;
            if (null != db && null != login)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetUserRole);
                db.AddInParameter(command, "@UserID", DbType.Int32, login.UserId);
            }
            return command;
        }
    }
}