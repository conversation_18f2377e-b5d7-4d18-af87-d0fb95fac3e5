﻿using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer
{
    public class GetTerminalListSelectionFactory : ISelectionFactory<LoginIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, LoginIdentity login)
        {
            DbCommand command = null;
            if (null != db )
            {
                command = db.GetStoredProcCommand(Constants.SP_GetTerminals);
            }
            return command;
        }
    }
}