﻿using System;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class CourierManifestIdentity : BaseEntity
    {
        #region Public Properties
        public string TerminalName { get; set; }
        public DateTime DateTime { get; set; }
        public string Routes { get; set; }
        public string DeviceId { get; set; }
        public DateTimeOffset DateTimeOffset { get; set; }
        #endregion

        #region Public Constructors
        public CourierManifestIdentity()
        {
        }
        #endregion
    }
}