﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class GetPudroAndRoutesSelectionFactory : ISelectionFactory<CourierManifestIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, CourierManifestIdentity courierManifest)
        {
            DbCommand command = null;
            if (null != db && null != courierManifest)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetPudroAndRoutes);
                db.AddInParameter(command, "@Terminal", DbType.String, courierManifest.TerminalName);
                db.AddInParameter(command, "@Date", DbType.Date, Helpers.ConvertDateTimeToString(courierManifest.DateTime));
            }
            return command;
        }
    }
}