﻿using System;
using System.Data;
using System.Data.Common;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class CourierManifestRepository : Repository<CourierManifestIdentity>
    {
        #region Public Constructors
        public CourierManifestRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion

        #region Public Methods
        

        public DataTableCollection GetCourierManifestReport(string terminalName, DateTime dateTime, DateTimeOffset dateTimeOffset, string routes, string userName)
        {
            ISelectionFactory<CourierManifestIdentity> selectionFactory = new GetCourierManifestReportSelectionFactory();                        
            
            CourierManifestIdentity cmi = new CourierManifestIdentity()
            {
                TerminalName = terminalName,
                DateTime = dateTime,
                Routes = routes,
                DateTimeOffset = dateTimeOffset,
                DeviceId = userName
            };
            return base.FindAll(selectionFactory, cmi);

        }

        public DataTable GetPudroAndRoutes(string terminalName, DateTime dateTime)
        {
            ISelectionFactory<CourierManifestIdentity> selectionFactory = new GetPudroAndRoutesSelectionFactory();
            CourierManifestIdentity cmi = new CourierManifestIdentity()
            {
                TerminalName = terminalName,
                DateTime = dateTime,
                Routes = String.Empty,
                DateTimeOffset = new DateTimeOffset(DateTime.Now),
            };            
            return Find(selectionFactory, cmi);
        }

        public bool CloseRoutes(string deviceId, string terminalName, string routes, DateTime dateTime, DateTimeOffset dateTimeOffset)
        {
            DbCommand outCommand = null;
            IUpdateFactory<CourierManifestIdentity> updateFactory = new CloseRouteUpdateFactory();
            CourierManifestIdentity cmi = new CourierManifestIdentity()
            {
                DeviceId = deviceId,
                TerminalName = terminalName,
                DateTime = dateTime,
                Routes = routes,
                DateTimeOffset = dateTimeOffset
            };
            Save(updateFactory, cmi, out outCommand);
            return Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 ? true : false;
        }
        #endregion
    }
}