﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class CloseRouteUpdateFactory : IUpdateFactory<CourierManifestIdentity>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, CourierManifestIdentity courierManifest)
        {
            DbCommand command = null;
            if (null != db && null != courierManifest)
            {
                command = db.GetStoredProcCommand(Constants.SP_CloseRoutes);
                db.AddInParameter(command, "@DeviceID", DbType.String, courierManifest.DeviceId);
                db.AddInParameter(command, "@Terminal", DbType.String, courierManifest.TerminalName);
                db.AddInParameter(command, "@Routes", DbType.String, courierManifest.Routes);
                db.AddInParameter(command, "@Datetimeoffset", DbType.DateTimeOffset,Helpers.ConvertDateTimeOffsetToString(courierManifest.DateTimeOffset));            
                db.AddInParameter(command, "@Date", DbType.Date , courierManifest.DateTime);
                db.AddOutParameter(command, "@Status", DbType.Boolean, 1);
            }
            return command;
        }
        #endregion
    }
}