﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class GetCourierManifestReportSelectionFactory : ISelectionFactory<CourierManifestIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, CourierManifestIdentity courierManifest)
        {
            DbCommand command = null;
            if (null != db && null != courierManifest)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetCourierManifestReport);
                db.AddInParameter(command, "@CallStack", DbType.String, null);
                db.AddInParameter(command, "@Terminal", DbType.String, courierManifest.TerminalName);
                db.AddInParameter(command, "@RouteNumber", DbType.String, courierManifest.Routes);
                db.AddInParameter(command, "@ManifestDate", DbType.Date, courierManifest.DateTime);
                db.AddInParameter(command, "@ManifestDateTimeOffset", DbType.DateTimeOffset, courierManifest.DateTimeOffset);
                db.AddInParameter(command, "@DeviceID", DbType.String, courierManifest.DeviceId);
            }
            return command;
        }
    }
}