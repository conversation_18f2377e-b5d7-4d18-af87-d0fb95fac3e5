﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.CourierManifest
{
    public class GetCourierManifestSelectionFactory : ISelectionFactory<CourierManifestIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, CourierManifestIdentity courierManifest)
        {
            DbCommand command = null;
            if (null != db && null != courierManifest)
            {                
                command = db.GetStoredProcCommand("dbo.GetCourierManifest_XML");
                db.AddInParameter(command, "@CallStack", DbType.String, null);
                db.AddInParameter(command, "@Terminal", DbType.String, courierManifest.TerminalName);
                db.AddInParameter(command, "@RouteNumber", DbType.String, courierManifest.Routes);
                db.AddInParameter(command, "@ManifestDate", DbType.Date, courierManifest.DateTime);    
            }
            return command;
        }
    }
}