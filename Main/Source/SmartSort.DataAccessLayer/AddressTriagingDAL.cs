﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer
{
   public class AddressTriagingDAL
    {
        public List<SmartSort.Business.Entities.AddressTriagingEntity> GetAddressTriagerecordFromDB(string Terminal, bool EscalatedAddress, string SortCriteria, int PageNumber, int NoofRecords)
        {
            List<AddressTriagingEntity> addressTriageQueueList = new List<AddressTriagingEntity>()
            {
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="ABC",Address="Pune", AddressID=1, AddressStatus="Unmatched",Pieces=2,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
              new AddressTriagingEntity(){EDD=DateTime.Now,Customer="PQR",Address="Mumbai",AddressID=2,AddressStatus="Unparssable",Pieces=3,LastUpdatedOn=DateTime.Now,LastUpdatedBy="ZYZ"},
            };
            return addressTriageQueueList;
        }

        public SmartSort.Business.Entities.AddressEntity GetAddressFromDB(int AddressID)
        {

            if (AddressID == 1)
            {
                AddressEntity addressDetails = new AddressEntity()
                {

                    PostalCode = "A9A9A9",
                    City = "Pune",
                    StreetNumber = "10",
                    StreetName = "MG Road",
                    Name = "ABC",
                    StreetSuffix = "2",
                    DirectionList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    SuffixList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    StreetTypeList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    ProvinceList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    ChangeResionList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } }
                };

                return addressDetails;
            }
            else 
            {
                AddressEntity addressDetails = new AddressEntity()
                {
                    PostalCode = "B9B9B9",
                    City = "Mumbai",
                    StreetNumber = "20",
                    StreetName = "SB Road",
                    Name = "PQR",
                    StreetSuffix = "2",
                    DirectionList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    SuffixList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    StreetTypeList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    ProvinceList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } },
                    ChangeResionList = new List<ListDetailsEntity>() { new ListDetailsEntity() { ID = 1, Name = "ABC" }, new ListDetailsEntity() { ID = 2, Name = "PQR" } }
                };
                return addressDetails;
            }



        }
    }
}
