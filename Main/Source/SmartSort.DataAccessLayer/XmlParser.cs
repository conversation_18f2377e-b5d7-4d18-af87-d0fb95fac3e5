﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Xml.Serialization;
using SmartSort.Common.Exception;

namespace SmartSort.DataAccessLayer
{
    public static class XmlParser
    {
        public static object ConvertXmlToBusinessObject<T>(string xmlString) where T : class, new()
        {
            XmlDocument xmldoc = new XmlDocument();
            xmldoc.LoadXml(xmlString);
            string rootName = xmldoc.DocumentElement.Name;
            string firstChildName = xmldoc.DocumentElement.FirstChild.Name;
            XmlNodeList node = xmldoc.SelectNodes(rootName + "/" + firstChildName);
            int Nodecount = node.Count;
            string xmlstring = string.Empty;
            if (Nodecount > 1)
            {
                XmlDocument docNew = new XmlDocument();
                XmlElement newRoot = docNew.CreateElement("ArrayOf" + firstChildName);
                docNew.AppendChild(newRoot);
                newRoot.InnerXml = xmldoc.DocumentElement.InnerXml;
                xmlstring = docNew.OuterXml;

                var businessObjectList = ToObject<List<T>>(xmlstring);
                return businessObjectList;
            }
            else
            {
                //string RemoveRootelement = xmlString.Replace("<" + rootName + ">", "");
                //xmlstring = RemoveRootelement.Replace("</" + rootName + ">", "");
                var businessObject = ToObject<T>(xmlString);
                return businessObject;
            }
        }

        public static T ToObject<T>(string xmlTextToParse) where T : class, new()
        {

            if (string.IsNullOrEmpty(xmlTextToParse))

                throw new XmlParserException("Invalid string input. Cannot parse an empty or null string.", new ArgumentException("xmlTestToParse"));

            var stringReader = new StringReader(xmlTextToParse);
            var serializer = new XmlSerializer(typeof(T));
            try
            {
                return serializer.Deserialize(stringReader) as T;
            }
            catch (Exception e)
            {
                throw new XmlParserException(string.Format("Unable to convert to given string into the type {0}. See inner exception for details.", typeof(T)), e);
            }
        }

    }
}
