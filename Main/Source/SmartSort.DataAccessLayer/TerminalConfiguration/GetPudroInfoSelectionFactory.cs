﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.TerminalConfiguration
{
    public class GetPudroInfoSelectionFactory : ISelectionFactory<Business.Entities.TerminalConfiguration>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.TerminalConfiguration terminalConfiguration)
        {
            DbCommand command = null;
            if (null != db && null != terminalConfiguration)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetPudroInfo);
                db.AddInParameter(command, "@CallStack", DbType.String,string.Empty);
                db.AddInParameter(command, "@Terminal", DbType.String, terminalConfiguration.Terminal);
            }
            return command;
        }
        #endregion Public Methods
    }
}
