﻿#region Using
using System;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;
using SmartSort.Common;
#endregion Using
namespace SmartSort.DataAccessLayer.TerminalConfiguration
{
     public class TerminalConfigurationRepository:Repository<SmartSort.Business.Entities.TerminalConfiguration>
    {
        #region Global Variables
        private DataTable dataTable;
        private DbCommand outCommand;       
        #endregion Global Variables

        #region Public Constructors

        public TerminalConfigurationRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion Public Constructors

        #region Public Methods
         /// <summary>
        /// Get Pudro Info
         /// </summary>
        /// <param name="terminal">terminal</param>
        /// <returns>DataTable</returns>
        public DataTable GetPudroInfo(string terminal)
        {
            ISelectionFactory<SmartSort.Business.Entities.TerminalConfiguration> selectionFactory = new GetPudroInfoSelectionFactory();
             Business.Entities.TerminalConfiguration terminalConfiguration = new Business.Entities.TerminalConfiguration {Terminal=terminal};
             dataTable = Find(selectionFactory,terminalConfiguration);
             return dataTable;
        }

        public int GetTotalActiveSides(string terminal)
        {
            ISelectionFactory<SmartSort.Business.Entities.TerminalConfiguration> selectionFactory = new GetPudroInfoSelectionFactory();
            Business.Entities.TerminalConfiguration terminalConfiguration = new Business.Entities.TerminalConfiguration { Terminal = terminal };
            var dataTables = FindAll(selectionFactory, terminalConfiguration);
            int totalActiveSides = 0;

            if (dataTables != null && dataTables.Count > 1 && dataTables[1].Rows.Count > 0)
            {
                Int32.TryParse(dataTables[1].Rows[0]["TotalActiveRoutes"].ToString(), out totalActiveSides);
            }
            
            return totalActiveSides;
        }

         /// <summary>
         /// Get Line Details
         /// </summary>
         /// <param name="terminal"></param>
         /// <param name="lineNumber"></param>
         /// <returns></returns>
        public DataTable GetLineDetails(string terminal,int lineNumber,out int totalRoutes)
        {
            ISelectionFactory<SmartSort.Business.Entities.TerminalConfiguration> selectionFactory = new GetLineDetailsSelectionFactory();
            Business.Entities.TerminalConfiguration terminalConfiguration = new Business.Entities.TerminalConfiguration { Terminal = terminal,LineNumber=lineNumber };
            dataTable = Find(selectionFactory, terminalConfiguration, out outCommand);
            totalRoutes = Convert.ToInt32(outCommand.Parameters["@TotalRoutes"].Value);
            return dataTable;
        }
        
        #endregion
    }

}
