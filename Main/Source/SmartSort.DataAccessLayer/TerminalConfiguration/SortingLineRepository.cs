﻿#region Using
using System;
using System.Data.Common;
using SmartSort.Business.Entities;

#endregion Using
namespace SmartSort.DataAccessLayer.TerminalConfiguration
{
    public class SortingLineRepository : Repository<SortingLineDetails>
    {
        #region Global Variables      
        private DbCommand outCommand;
        #endregion Global Variables

        #region Public Constructors

        public SortingLineRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion Public Constructors

        #region Public Methods
        /// <summary>
        /// 
        /// </summary>
        /// <param name="List<SortingLine>"></param>      
        /// <returns>bool</returns>
        public bool UpdateLineDetails(SortingLineDetails sortingLineDetails)
        {
            bool saveStatus = false;
            IUpdateFactory<SortingLineDetails> updateFactory = new SortingLineItemUpdateFactory();
            Save(updateFactory, sortingLineDetails, out outCommand);
            return saveStatus = Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 ? true : false;
        }
        #endregion Public Methods
    }
}
