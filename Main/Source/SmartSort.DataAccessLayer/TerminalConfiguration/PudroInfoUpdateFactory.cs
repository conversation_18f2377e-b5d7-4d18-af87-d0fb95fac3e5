﻿using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using System.Data;
using System.Data.Common;
namespace SmartSort.DataAccessLayer.TerminalConfiguration
{
    public class PudroInfoUpdateFactory : IUpdateFactory<Business.Entities.TerminalConfiguration>
    {

        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, Business.Entities.TerminalConfiguration terminalConfiguration)
        {
            DbCommand command = null;
            if (null != db && null != terminalConfiguration)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdatePudroInfo);
                db.AddInParameter(command, "@CallStack   ", DbType.String, terminalConfiguration.CallStack);
                db.AddInParameter(command, "@Terminal  ", DbType.String, terminalConfiguration.Terminal);
                db.AddInParameter(command, "@TermConfID ", DbType.Int32, terminalConfiguration.TermConfID);
                db.AddInParameter(command, "@LineNumber ", DbType.Int32, terminalConfiguration.LineNumber);
                db.AddInParameter(command, "@PrimarySort", DbType.String, terminalConfiguration.PrimarySort);
                db.AddInParameter(command, "@LineDescription", DbType.String, terminalConfiguration.LineDescription);
                db.AddInParameter(command, "@DoorsOnLeft ", DbType.Int32, terminalConfiguration.RoutesOnLeft);
                db.AddInParameter(command, "@DoorsOnRight ", DbType.Int32, terminalConfiguration.RoutesOnRight);
                db.AddInParameter(command, "@DoorsOnCentre ", DbType.Int32, terminalConfiguration.RoutesOnCentre);
                db.AddInParameter(command, "@Active ", DbType.String, terminalConfiguration.Active);
                db.AddInParameter(command, "@LastModifyBy ", DbType.String, terminalConfiguration.LastModifyBy);
                db.AddInParameter(command, "@LastUpdateDateTime ", DbType.DateTime, terminalConfiguration.LastUpdateDateTime);
                db.AddOutParameter(command, "@NewTermConfID", DbType.Int32, 10);
                db.AddOutParameter(command, "@Status", DbType.String, 10);
            }
            return command;
        }

        #endregion Public Methods
    }
}
