﻿using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.TerminalConfiguration
{
    class SortingLineItemUpdateFactory : IUpdateFactory<Business.Entities.SortingLineDetails>
    {

        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, Business.Entities.SortingLineDetails sortingLineDetails)
        {
            DbCommand command = null;
            if (null != db && null != sortingLineDetails)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateLineDetails);             

                string LineDetailsxml=SmartSort.Common.TransformEntity.ObjectToXML(sortingLineDetails);
                db.AddInParameter(command, "@LineDetailsxml", DbType.Xml, LineDetailsxml);
                db.AddOutParameter(command, "@Status", DbType.String, 10);
            }
            return command;
        }

        #endregion Public Methods
    }
}