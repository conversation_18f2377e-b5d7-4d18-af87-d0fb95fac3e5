﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.GlobalConfiguration
{
    /// <summary>
    /// Update factory for Global config.
    /// </summary>
    internal class GlobalConfigurationUpdateFactory : IUpdateFactory<SmartSort.Business.Entities.GlobalConfiguration>
    {
        public DbCommand ConstructUpdateCommand(Database db, SmartSort.Business.Entities.GlobalConfiguration config)
        {
            DbCommand command = null;
            if (null != db && null != config)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateGlobalConfig);
                db.AddInParameter(command, "@SupportPager", DbType.String, config.pager);
            }
            return command;
        }
    }
}