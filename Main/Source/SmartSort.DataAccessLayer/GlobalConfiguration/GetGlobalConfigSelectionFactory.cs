﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.GlobalConfiguration
{
    public class GetGlobalConfigSelectionFactory : ISelectionFactory<SmartSort.Business.Entities.GlobalConfiguration>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, SmartSort.Business.Entities.GlobalConfiguration id)
        {
            DbCommand command = db.GetStoredProcCommand(Constants.SP_GetGlobalConfig);                        
            return command;
        }
        #endregion
    }
}