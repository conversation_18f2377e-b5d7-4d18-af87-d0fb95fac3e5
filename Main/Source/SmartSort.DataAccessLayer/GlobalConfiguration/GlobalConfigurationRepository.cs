﻿using System;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;


namespace SmartSort.DataAccessLayer.GlobalConfiguration
{
    public class GlobalConfigurationRepository : Repository<SmartSort.Business.Entities.GlobalConfiguration>
    {
        #region Public Constructors
        public GlobalConfigurationRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion

        #region Public Methods
        

        public DataTable GetGlobalConfiguration()
        {
            ISelectionFactory<SmartSort.Business.Entities.GlobalConfiguration> selectionFactory = new GetGlobalConfigSelectionFactory();            
            return Find(selectionFactory, null);
        }

        public bool Update(SmartSort.Business.Entities.GlobalConfiguration config)
        {            
            IUpdateFactory<SmartSort.Business.Entities.GlobalConfiguration> updateFactory = new GlobalConfigurationUpdateFactory();
            Save(updateFactory, config);
            return true;
        }
        
        #endregion
    }
}