﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;


namespace SmartSort.DataAccessLayer.SortPlanManagement
{
    public class GetSortPlanInfoSelectionFactory : ISelectionFactory<RouteSortPlan>
    {
        public DbCommand ConstructSelectCommand(Database db, RouteSortPlan sortPlan)
        {
            DbCommand command = null;
            if (db != null && sortPlan != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetSortPlanInfo);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@SortPlanID", DbType.String, sortPlan.SortPlanId);
                db.AddInParameter(command, "@CallStack", DbType.String, null);
            }
            return command;
        }
    }
}