﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;


namespace SmartSort.DataAccessLayer.SortPlanManagement
{
    class GetActiveRoutePlanSelectionFactory : ISelectionFactory<RouteSortPlan>
    {
        public DbCommand ConstructSelectCommand(Database db, RouteSortPlan sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetActiveRoutePlan);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@CallStack", DbType.String, string.Empty);
            }
            return command;
        }
    }
}