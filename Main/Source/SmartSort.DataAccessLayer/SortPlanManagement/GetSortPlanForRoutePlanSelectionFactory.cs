﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace SmartSort.DataAccessLayer.SortPlanManagement
{
    class GetSortPlanForRoutePlanSelectionFactory : ISelectionFactory<RouteSortPlan>
    {
        public DbCommand ConstructSelectCommand(Database db, RouteSortPlan sortPlan)
        {
            DbCommand command = null;
            if (null != db && null != sortPlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetSortPlanForRoutePlan);
                db.AddInParameter(command, "@Terminal", DbType.String, sortPlan.Terminal);
                db.AddInParameter(command, "@RoutePlanCodeId", DbType.String, sortPlan.RoutePlanCodeId);                
                db.AddInParameter(command, "@CallStack", DbType.String, string.Empty);                         
            }
            return command;
        }
    }
}