﻿using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;
using System;

namespace SmartSort.DataAccessLayer.SortPlanManagement
{
    public class SortPlanManagementRepository : Repository<SortPlanManagementItem>
    {
        #region Public Constructors
        public SortPlanManagementRepository(string databaseName)
            : base(databaseName)
        {
        }
        #endregion

        #region Public Methods  
        /// <summary>
        /// GetSortPlanForRoutePlan
        /// </summary>
        /// <param name="terminal">terminal</param>
        /// <param name="sortPlanId">sortPlanId</param>
        /// <returns>DataTable</returns>
        public DataTable GetSortPlanForRoutePlan(string terminal, int routePlanCodeId) 
        {
            ISelectionFactory<RouteSortPlan> selectionFactory = new GetSortPlanForRoutePlanSelectionFactory();
            RouteSortPlan sortPlan = new RouteSortPlan() { Terminal = terminal, RoutePlanCodeId = routePlanCodeId };
            DataTable dtSortPlan = Find(selectionFactory, sortPlan);           
            return dtSortPlan;
        }

        /// <summary>
        /// GetActiveRoutePlan
        /// </summary>
        /// <param name="terminal">terminal</param>
        /// <returns>DataTable</returns>
        public DataTable GetActiveRoutePlan(string terminal)
        {
            ISelectionFactory<RouteSortPlan> selectionFactory = new GetActiveRoutePlanSelectionFactory();
            RouteSortPlan sortPlan = new RouteSortPlan() { Terminal = terminal };
            DataTable dtActiveRoutePlan = Find(selectionFactory, sortPlan);
            return dtActiveRoutePlan;
        }

        public DataTableCollection GetSortPlanInfo(string terminal, int sortPlanId)
        {
            ISelectionFactory<RouteSortPlan> selectionFactory = new GetSortPlanInfoSelectionFactory();
            RouteSortPlan sortPlan = new RouteSortPlan() { Terminal = terminal, SortPlanId = sortPlanId };
            DataTableCollection dataCollection = FindAll(selectionFactory, sortPlan);
            return dataCollection;
        }

        public bool UpdateSortPlanInfo(SortPlanManagementItem sortPlanManagementItem)
        {
            DbCommand outValue = null;
            IUpdateFactory<SortPlanManagementItem> updateFactory = new SortPlanInfoUpdateFactory();
            bool isSuccess = Save(updateFactory, sortPlanManagementItem, out outValue) && outValue != null && 
                outValue.Parameters != null && outValue.Parameters.Contains("@Status") && Convert.ToInt32(outValue.Parameters["@Status"].Value) == 0;
            return isSuccess; 
        }
        #endregion
    }
}
