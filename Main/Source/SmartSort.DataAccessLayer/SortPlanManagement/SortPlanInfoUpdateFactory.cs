﻿using System;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.SortPlanManagement
{
    public class SortPlanInfoUpdateFactory : IUpdateFactory<SortPlanManagementItem>
    {
        #region Public Methods
        public DbCommand ConstructUpdateCommand(Database db, SortPlanManagementItem sortPlanManagementItem)
        {
            DbCommand command = null;
            if (db != null && sortPlanManagementItem != null && sortPlanManagementItem.PudroConfigrationItems != null)
            {
                string parkingPlanDetailsXml = TransformEntity.ObjectToXML(sortPlanManagementItem.PudroConfigrationItems);
                string sortPlanDefault = sortPlanManagementItem.IsDefaultSortPlan ? Constants.YesShortDbValue : Constants.NoShortDbValue;

                if (!String.IsNullOrWhiteSpace(parkingPlanDetailsXml))
                {
                    command = db.GetStoredProcCommand(Constants.SP_UpdateSortPlanInfo);

                    db.AddInParameter(command, "@Terminal", DbType.String, sortPlanManagementItem.TerminalName);
                    db.AddInParameter(command, "@SortPlanId", DbType.Int32, sortPlanManagementItem.SortPlanId);
                    db.AddInParameter(command, "@RoutePlanCodeId", DbType.Int32, sortPlanManagementItem.RoutePlanCodeId);
                    db.AddInParameter(command, "@ParkingPlanCodeId", DbType.Int32, sortPlanManagementItem.ParkingPlanCodeId);
                    db.AddInParameter(command, "@SortPlanCode", DbType.String, sortPlanManagementItem.SortPlanName);
                    db.AddInParameter(command, "@SortPlanDescription", DbType.String, sortPlanManagementItem.SortPlanDescription);
                    db.AddInParameter(command, "@ParkingPlanCode", DbType.String, sortPlanManagementItem.ParkingPlanName);
                    db.AddInParameter(command, "@ParkingPlanDescription", DbType.String, sortPlanManagementItem.ParkingPlanDescription);
                    db.AddInParameter(command, "@SortPlanDefault", DbType.String, sortPlanDefault);
                    db.AddInParameter(command, "@UnassignedRoutes", DbType.Int32, sortPlanManagementItem.UnassignedRouteCount);
                    db.AddInParameter(command, "@Username", DbType.String, sortPlanManagementItem.UserName);
                    db.AddInParameter(command, "@UpdateDatetime", DbType.DateTime, sortPlanManagementItem.ParkingPlanLastUpdateDateTime);
                    db.AddInParameter(command, "@PPDetailsXML", DbType.Xml, parkingPlanDetailsXml);
                    db.AddOutParameter(command, "@Status", DbType.Boolean, 1);
                }
            }
            return command;
        }
        #endregion
    }
}