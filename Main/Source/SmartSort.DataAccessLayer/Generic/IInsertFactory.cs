﻿using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace SmartSort.DataAccessLayer
{
    /// <summary>
    /// Interface to factory class that generates DbCommand to
    /// insert a new domain object into the database.
    /// </summary>
    /// <typeparam name="TDomainObject">Type of domain object to insert.</typeparam>
    public interface IInsertFactory<TDomainObject>
    {
        /// <summary>
        /// Generate the insert command.
        /// </summary>
        /// <param name="db">Entlib Database object to generate command for.</param>
        /// <param name="domainObj">Domain object to insert.</param>
        /// <returns>Initialized DbCommand object.</returns>
        DbCommand ConstructInsertCommand(Database db, TDomainObject domainObj);
    }
}