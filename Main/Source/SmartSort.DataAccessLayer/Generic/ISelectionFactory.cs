﻿using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace SmartSort.DataAccessLayer
{
    /// <summary>
    /// This interface provides the signature for the
    /// class that converts a given IdentityObject into
    /// a DbCommand to do a search for the corresponding
    /// result set.
    /// </summary>
    /// <typeparam name="TIdentityObject">Type that identifies the
    /// items to search for.</typeparam>
    public interface ISelectionFactory<TIdentityObject>
    {
        DbCommand ConstructSelectCommand(Database db, TIdentityObject idObject);
    }
}