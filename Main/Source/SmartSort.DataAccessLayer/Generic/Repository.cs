﻿using System;
using System.Data;
using System.Data.Common;
using System.Linq;
using Microsoft.Practices.EnterpriseLibrary.Data;

namespace SmartSort.DataAccessLayer
{
    /// <summary>
    /// A generic repository base class that uses various
    /// factory classes to get and retrieve specific domain
    /// objects.
    /// </summary>
    public class Repository<TDomainObject>
    {
        private string databaseName;
        protected Database db;

        public Repository(string databaseName)
        {
            this.databaseName = databaseName;
            DatabaseProviderFactory factory = new DatabaseProviderFactory();
            db = factory.Create(databaseName);
        }

        public Repository(Database database)
        {
            db = database;
        }

        public DataTable Find<TIdentity>(
            ISelectionFactory<TIdentity> selectionFactory,
            TIdentity identity)
        {
            DataTable dataTable = null;
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                DataSet dataSet = db.ExecuteDataSet(command);
                if (dataSet.Tables.Count > 0)
                    dataTable = dataSet.Tables[0];
            }

            return dataTable;
        }

        public TDomainObject FindOne<TIdentity>(
           ISelectionFactory<TIdentity> selectionFactory,
           IDomainObjectFactory<TDomainObject> domainObjectFactory,
           TIdentity identity)
        {
            TDomainObject result = default(TDomainObject);
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                using (IDataReader rdr = db.ExecuteReader(command))
                {
                    if (rdr.Read())
                    {
                        result = domainObjectFactory.Construct(rdr);
                    }
                }

            }

            return result;
        }



        public DataTableCollection FindAll<TIdentity>(
          ISelectionFactory<TIdentity> selectionFactory,
          TIdentity identity)
        {
            DataTableCollection dataTableCollection = null;
            if (null != selectionFactory)
            {
                using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
                {
                    DataSet dataSet = db.ExecuteDataSet(command);

                    if (dataSet != null)
                    {
                        dataTableCollection = dataSet.Tables;
                    }
                }
            }

            return dataTableCollection;
        }

        public DataTableCollection FindAll<TIdentity>(
          ISelectionFactory<TIdentity> selectionFactory,
          TIdentity identity, out DbCommand outValue)
        {
            DataTableCollection dataTableCollection = null;
            outValue = null;
            if (null != selectionFactory)
            {
                using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
                {
                    DataSet dataSet = db.ExecuteDataSet(command);
                    outValue = command;

                    if (dataSet != null)
                    {
                        dataTableCollection = dataSet.Tables;
                    }
                }
            }
            return dataTableCollection;
        }

        public DataTable Find<TIdentity>(
          ISelectionFactory<TIdentity> selectionFactory,
          TIdentity identity, out DbCommand outValue)
        {
            DataTable dataTable = null;
            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                DataSet dataSet = db.ExecuteDataSet(command);
                outValue = command;
                if (dataSet.Tables.Count > 0)
                    dataTable = dataSet.Tables[0];
            }

            return dataTable;
        }

        public bool Add(IInsertFactory<TDomainObject> insertFactory, TDomainObject domainObj)
        {
            using (DbCommand command = insertFactory.ConstructInsertCommand(db, domainObj))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command));
            }
        }

        public bool Add(IInsertFactory<TDomainObject> insertFactory, TDomainObject domainObj, DbTransaction trans)
        {
            using (DbCommand command = insertFactory.ConstructInsertCommand(db, domainObj))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command, trans));
            }
        }

        public bool Remove<TIdentityObject>(IDeleteFactory<TIdentityObject> deleteFactory,
            TIdentityObject identityObj)
        {
            using (DbCommand command = deleteFactory.ConstructDeleteCommand(db, identityObj))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command));
            }
        }
        public bool Remove<TIdentityObject>(IDeleteFactory<TIdentityObject> deleteFactory,
           TIdentityObject identityObj, out DbCommand outValue)
        {
            using (DbCommand command = deleteFactory.ConstructDeleteCommand(db, identityObj))
            {
                bool result = Convert.ToBoolean(db.ExecuteNonQuery(command));
                outValue = command;
                return result;
            }
        }
        public bool Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command));
            }
        }

        public bool Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj, out DbCommand outValue)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                bool result = Convert.ToBoolean(db.ExecuteNonQuery(command));
                outValue = command;
                return result;
            }
        }

        public DataTable Save<TIdentity>(
           ISelectionFactory<TIdentity> selectionFactory,
           TIdentity identity)
        {
            DataTable dataTable = null;           

            using (DbCommand command = selectionFactory.ConstructSelectCommand(db, identity))
            {
                DataSet dataSet = db.ExecuteDataSet(command);
                foreach (DataTable table in dataSet.Tables.Cast<DataTable>().Where(table => table.Rows.Count != 0))
                {
                    dataTable = dataSet.Tables[0];
                }
            }

            return dataTable;
        }

        public bool Save(IUpdateFactory<TDomainObject> updateFactory, TDomainObject domainObj, DbTransaction trans)
        {
            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, domainObj))
            {
                return Convert.ToBoolean(db.ExecuteNonQuery(command, trans));
            }
        }
    }
}