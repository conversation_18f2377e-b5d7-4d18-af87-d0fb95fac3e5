﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Business.Entities;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetAddressTriageSelectionFactory : ISelectionFactory<AddressRequest>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, AddressRequest addressRequest)
        {
            DbCommand command = null;
            if (null != db && null != addressRequest)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetTriageQueue);
                db.AddInParameter(command, "@Terminal", DbType.String, addressRequest.Terminal);
                db.AddInParameter(command, "@EscalatedAddressOnly", DbType.String, addressRequest.EscalatedAddress);
                db.AddInParameter(command, "@PageNumber", DbType.Int32, addressRequest.PageNumber);
                db.AddInParameter(command, "@RowsPerPage", DbType.Int32, addressRequest.RowsPerPage);
                db.AddInParameter(command, "@SortColumn", DbType.String, addressRequest.SortCriteria);
                db.AddInParameter(command, "@SortDirection", DbType.String, addressRequest.SortDirection);
                db.AddOutParameter(command, "@NoOfAddresses", DbType.Int32, 2);
            }
            // addressRequest.NoOfAddresses = Convert.ToInt32(db.GetParameterValue(command, "NoOfAddresses"));
            return command;
        }

        #endregion Public Methods
    }
}