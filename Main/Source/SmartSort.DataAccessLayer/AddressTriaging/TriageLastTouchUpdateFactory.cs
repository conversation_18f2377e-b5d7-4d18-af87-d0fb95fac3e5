﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class TriageLastTouchUpdateFactory : IUpdateFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
             DbCommand command = null;
             if (null != db && null != addressTriages)
             {
                 command = db.GetStoredProcCommand(Constants.SP_UpdateTriageLastTouch);
            db.AddInParameter(command, "@TriageQueueID", DbType.Int32, addressTriages.TriageQueueID);
            db.AddInParameter(command, "@UserName", DbType.String, addressTriages.UserName);
            db.AddInParameter(command, "@UpdateDateTimeOffset", DbType.DateTimeOffset, Helpers.ConvertDateTimeOffsetToString(addressTriages.UpdateDateTimeOffset));
            db.AddOutParameter(command, "@Status", DbType.Boolean, 10);
             }
            return command;
        }

        #endregion Public Methods
    }
}