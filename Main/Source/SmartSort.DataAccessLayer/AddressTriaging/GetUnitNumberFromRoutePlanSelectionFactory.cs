﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetUnitNumberFromRoutePlanSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods
        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (db != null && addressTriages != null)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetUnitNumberFromRoutePlan);
                db.AddInParameter(command, "@Terminal", DbType.Int32, addressTriages.Terminal);
                db.AddInParameter(command, "@UnitNumber", DbType.String, addressTriages.Suite);                 
                db.AddInParameter(command, "@postalcode", DbType.String, addressTriages.PostalCode); 

            }
            return command;
        }
        #endregion
    }
}