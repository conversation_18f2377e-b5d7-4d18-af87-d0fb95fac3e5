﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetAddressSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Method

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriagings)
        {
            DbCommand command = null;
            if (null != db && null != addressTriagings)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetTriageAddress);
                db.AddInParameter(command, "@TriageQueueID", DbType.Int32, addressTriagings.TriageQueueID);
            }
            return command;
        }

        #endregion Public Method
    }
}