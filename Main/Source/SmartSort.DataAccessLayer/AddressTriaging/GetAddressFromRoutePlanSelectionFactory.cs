﻿#region Using

using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

#endregion Using

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetAddressFromRoutePlanSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAddressFromRoutePlan);
                db.AddInParameter(command, "@Terminal", DbType.Int32, addressTriages.Terminal);
                db.AddInParameter(command, "@postalcode", DbType.String, addressTriages.PostalCode);
            }
            return command;
        }

        #endregion Public Methods
    }
}