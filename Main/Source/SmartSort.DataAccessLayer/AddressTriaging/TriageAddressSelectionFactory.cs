﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class TriageAddressSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
     

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateTriageAdress);
                db.AddInParameter(command,  "@TriageQueueID", DbType.Int32, addressTriages.TriageQueueID);
                db.AddInParameter(command,  "@Terminal", DbType.String, addressTriages.Terminal);
                db.AddInParameter(command,  "@CompanyName", DbType.String, addressTriages.DeclareCompanyName);
                db.AddInParameter(command,  "@StreetNumber", DbType.String, addressTriages.DeclareStreetNumber);
                db.AddInParameter(command,  "@StreetNumSuf", DbType.String, addressTriages.StreetNumSuf);
                db.AddInParameter(command,  "@Suite", DbType.String, addressTriages.Suite);
                db.AddInParameter(command,  "@StreetName", DbType.String, addressTriages.StreetName);
                db.AddInParameter(command,  "@StreetType", DbType.String, addressTriages.StreetType);
                db.AddInParameter(command,  "@StreetDir", DbType.String, addressTriages.StreetDirection);
                db.AddInParameter(command,  "@City", DbType.String, addressTriages.City);
                db.AddInParameter(command,  "@PostalCode", DbType.String, addressTriages.PostalCode);
                db.AddInParameter(command,  "@Province", DbType.String, addressTriages.ProvinceCode);
                db.AddInParameter(command,  "@UserName", DbType.String, addressTriages.UserName);
                db.AddInParameter(command,  "@Pieces", DbType.Int32, addressTriages.Pieces);
                db.AddInParameter(command,  "@TriageDescription", DbType.String, addressTriages.TriageReasonCode);
                db.AddInParameter(command,  "@UpdateDateTimeOffset", DbType.DateTimeOffset, Helpers.ConvertDateTimeOffsetToString(addressTriages.UpdateDateTimeOffset));
                db.AddOutParameter(command, "@Status", DbType.Boolean, 10);
                db.AddOutParameter(command, "@NotExist", DbType.Boolean, 10);                
            }
            return command;
      
        }
    }
}