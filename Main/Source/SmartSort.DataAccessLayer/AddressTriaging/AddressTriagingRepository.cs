﻿#region Using

using System;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;
using SmartSort.Common;
using System.Collections.Generic;

#endregion Using

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class AddressTriagingRepository : Repository<Business.Entities.AddressTriaging>
    {
        #region Global Variables

        private DataTable dataTable;
        private DbCommand outCommand;
        private bool saveStatus;

        #endregion Global Variables

        #region Public Constructors

        public AddressTriagingRepository(string databaseName)
            : base(databaseName)
        {
        }

        #endregion Public Constructors

        #region Public Methods

        /// <summary>
        /// Get Address of Triage Records
        /// </summary>
        /// <param name="addressRequest">addressRequest</param>
        /// <param name="noOfRecords">NoOfrecords</param>
        /// <returns>DataTable of AddressTriageQueue</returns>
        public DataTable GetAddressTriageRecord(AddressRequest addressRequest, out int noOfRecords)
        {
            noOfRecords = Constants.DEFAULTINTEGER_VALUE;
            ISelectionFactory<AddressRequest> selectionFactory = new GetAddressTriageSelectionFactory();
            dataTable = Find(selectionFactory, addressRequest, out outCommand);
            noOfRecords = Convert.ToInt32(outCommand.Parameters["@NoOfAddresses"].Value);
            return dataTable;
        }

        /// <summary>
        /// Get Address
        /// </summary>
        /// <param name="triageQueueId">TriageQueueID</param>
        /// <returns>DataTable of Address</returns>
        public DataTable GetAddress(int triageQueueId)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAddressSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {TriageQueueID = triageQueueId};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get Triage Pin Details
        /// </summary>
        /// <param name="triageQueueId">TriageQueueID</param>
        /// <returns>DataTable of Triage Pins</returns>
        public DataTable GetTriagePinDetails(int triageQueueId)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetTriagePinDetailsSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {TriageQueueID = triageQueueId};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get Triage Reason Code
        /// </summary>
        /// <returns>DataTable of Triage Reason Code</returns>
        public DataTable GetTriageReasonCodes()
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetTriageReasonCodesSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging();
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
           
        }


        public DataTable GetAddressFromRoutePlanById(int Id)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAddressFromRoutePlanByIdSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                AddressID = Id                
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get Address From Rout Plan
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="postalCode">PostalCode</param>
        /// <returns>DataTable of Address</returns>
        public DataTable GetAddressFromRoutePlan(string terminal, string postalCode)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAddressFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                PostalCode = postalCode
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get Street Name
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="streetName">streetName</param>
        /// <param name="postalCode"></param>
        /// <returns>Data table of Street Name</returns>
        public DataTable GetStreetNameFromRoutePlan(string terminal, string streetName,string postalCode)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetStreetNameFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                StreetName = streetName,
                PostalCode = postalCode
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        public DataTable GetCustomerNameFromRoutePlan(string terminal, string customerName, string postalCode)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetCustomerNameFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                CustomerName = customerName,
                PostalCode = postalCode
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }


        public DataTable GetUnitNumberFromRoutePlan(string terminal, string UnitNumber, string postalCode)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetUnitNumberFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                Suite = UnitNumber,
                PostalCode = postalCode
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        
        

        public DataTable GetCustomerNameFromRoutePlan(string terminal, string postalCode)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetCustomerNameFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                PostalCode = postalCode
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get All Route and Self from route plan
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <returns>DataTable of Route and Self</returns>

        public DataTable GetAllRouteAndShelfFromRoutePlan(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAllRouteAndShelfFromRoutePlanSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get Provinces List
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <returns>DataTable of Provinces</returns>
        public DataTable GetProvincesList(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetProvincesSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Getting Street Suffix List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>DataTable StreetSuffix List</returns>
        public DataTable GetStreetSuffixList(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetStreetSuffixSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Getting Street Direction List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>DataTable StreetDirection List</returns>
        public DataTable GetStreetDirectionList(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetStreetDirectionSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Getting Street Types List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>DataTable StreetTypesList</returns>
        public DataTable GetStreetTypesList(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetStreetTypesSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get All Routes
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <returns>DataTalbe for All Routes</returns>
        public DataTable GetAllRoutes(string terminal)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAllRoutesSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging {Terminal = terminal};
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Get All Shelf for Route
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="routes">Routes</param>
        /// <returns>DataTable of  Shelf's</returns>
        public DataTable GetAllShelfForRoute(string terminal, string routes)
        {
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new GetAllShelfsForRouteSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                Terminal = terminal,
                Route = routes
            };
            dataTable = Find(selectionFactory, addressTriagings);
            return dataTable;
        }

        /// <summary>
        /// Update Triage Address
        /// </summary>
        /// <param name="addressDetails">addressDetails</param>
        /// <param name="status">Status</param>
        /// <param name="route">Route</param>
        /// <param name="shelf">Shelf</param>
        /// <returns>DataTable</returns>
        public void UpdateTriageAddress(AddressDetails addressDetails, out bool status, out bool alreadyResolved, out string route, out string shelf)
        {
            status = false;
            alreadyResolved = false;
            route = string.Empty;
            shelf = string.Empty;
            ISelectionFactory<Business.Entities.AddressTriaging> selectionFactory = new TriageAddressSelectionFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging();
            if (addressDetails != null)
            {
                addressTriagings.TriageQueueID = addressDetails.TriageQueueID;
                addressTriagings.Terminal = addressDetails.Terminal;
                addressTriagings.DeclareCompanyName = addressDetails.DeclareCompanyName;
                addressTriagings.DeclareStreetNumber = addressDetails.DeclareStreetNumber;                
                addressTriagings.StreetNumSuf = addressDetails.StreetSuffix;
                addressTriagings.Suite = addressDetails.SuiteNumber;
                addressTriagings.StreetName = addressDetails.StreetName;
                addressTriagings.StreetType = addressDetails.StreetType;
                addressTriagings.StreetDirection = addressDetails.StreetDirection;
                addressTriagings.City = addressDetails.City;
                addressTriagings.PostalCode = addressDetails.PostalCode;
                addressTriagings.ProvinceCode = addressDetails.Province;
                addressTriagings.UserName = addressDetails.UserName;
                addressTriagings.Pieces = Convert.ToInt32(addressDetails.Pieces);
                addressTriagings.TriageReasonCode = addressDetails.TriageReasonCode;
                addressTriagings.UpdateDateTimeOffset = addressDetails.UpdateDateTimeOffset;
            }
            dataTable = Find(selectionFactory, addressTriagings,out outCommand);
            if (dataTable != null)
            {
                route = dataTable.Rows[0]["Route"].ToString();
                shelf = dataTable.Rows[0]["Shelf"].ToString();
            }
            status = Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 ? true : false;
            alreadyResolved = Convert.ToInt32(outCommand.Parameters["@NotExist"].Value) == 0 ? false : true; 
        }

        /// <summary>
        /// Update Triage Last Touch
        /// </summary>
        /// <param name="triageQueueId">Triage Queue ID</param>
        /// <param name="userName">UserName</param>
        /// <param name="updateDateTimeOffset">UpdateDateTimeOffset</param>
        /// <returns>Return True/False</returns>
        public bool UpdateTriageLastTouch(int triageQueueId, string userName, DateTimeOffset updateDateTimeOffset)
        {            
            IUpdateFactory<Business.Entities.AddressTriaging> updateFactory = new TriageLastTouchUpdateFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging
            {
                TriageQueueID = triageQueueId,
                UserName = userName,
                UpdateDateTimeOffset = updateDateTimeOffset,
                Status = string.Empty
            };
            Save(updateFactory, addressTriagings, out outCommand);
            return saveStatus = Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 ? true : false;
        }


        public bool DeleteAddresses(List<int> ids)
        {
            IUpdateFactory<AddressTriageListIdentity> updateFactory = new DeleteAddressesUpdateFactory();
            List<AddressTriageQueueIdentity> list = new List<AddressTriageQueueIdentity>();
            AddressTriageListIdentity idList = new AddressTriageListIdentity();
            foreach (int id in ids)
            {
                list.Add(new AddressTriageQueueIdentity(id));
            }
            idList.Ids = list;

            using (DbCommand command = updateFactory.ConstructUpdateCommand(db, idList))
            {
                db.ExecuteNonQuery(command);
                return Convert.ToInt32(command.Parameters["@Status"].Value) == 0;
            }          
        }

        /// <summary>
        /// Update Route and shelf
        /// </summary>
        /// <param name="addressDetails">addressDetails</param>
        /// <param name="status">Status</param>
        /// <returns>True/False</returns>
        public bool UpdateRouteAndShelf(AddressDetails addressDetails, out string status)
        {
            status = string.Empty;
            IUpdateFactory<Business.Entities.AddressTriaging> updateFactory = new RouteAndSelfUpdateFactory();
            Business.Entities.AddressTriaging addressTriagings = new Business.Entities.AddressTriaging();
            if (addressDetails != null)
            {
                addressTriagings.TriageQueueID = addressDetails.TriageQueueID;
                addressTriagings.Route = addressDetails.Route;
                addressTriagings.Shelf = addressDetails.Shelf;
                addressTriagings.Pieces = Convert.ToInt32(addressDetails.Pieces);
                addressTriagings.UserName = addressDetails.UserName;
                addressTriagings.BeltSide = addressDetails.BeltSide;
                addressTriagings.PrimarySort = addressDetails.PrimarySort;
                addressTriagings.UpdateDateTimeOffset = addressDetails.UpdateDateTimeOffset;
            }
            addressTriagings.Status = status;
            Save(updateFactory, addressTriagings, out outCommand);
            return saveStatus = Convert.ToInt32(outCommand.Parameters["@Status"].Value) == 0 ? true : false;
        }

        #endregion Public Methods
    }//Class
}//NameSpace