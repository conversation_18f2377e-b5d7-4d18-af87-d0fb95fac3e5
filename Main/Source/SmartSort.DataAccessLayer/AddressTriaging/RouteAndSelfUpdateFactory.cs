﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class RouteAndSelfUpdateFactory : IUpdateFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_UpdateRouteAndShelf);
            db.AddInParameter(command, "@TriageQueueID ", DbType.Int32, addressTriages.TriageQueueID);
            db.AddInParameter(command, "@Route ", DbType.String, addressTriages.Route);
            db.AddInParameter(command, "@Shelf", DbType.String, addressTriages.Shelf);
            db.AddInParameter(command, "@Pieces", DbType.Int32, addressTriages.Pieces);
            db.AddInParameter(command, "@UserName ", DbType.String, addressTriages.UserName);
            db.AddInParameter(command, "@UpdateDateTimeOffset ", DbType.DateTimeOffset, Helpers.ConvertDateTimeOffsetToString(addressTriages.UpdateDateTimeOffset));           
            db.AddInParameter(command, "@BeltSide ", DbType.String, addressTriages.BeltSide);
            db.AddInParameter(command, "@PrimarySort ", DbType.String, addressTriages.PrimarySort);
            db.AddOutParameter(command, "@Status", DbType.String, 10);
            }
            return command;
        }

        #endregion Public Methods
    }
}