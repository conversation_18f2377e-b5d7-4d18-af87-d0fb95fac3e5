﻿#region Using

using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

#endregion Using

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetAddressFromRoutePlanByIdSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAddressFromRoutePlanById);
                db.AddInParameter(command, "@Id", DbType.Int32, addressTriages.AddressID);                
            }
            return command;
        }

        #endregion Public Methods
    }
}