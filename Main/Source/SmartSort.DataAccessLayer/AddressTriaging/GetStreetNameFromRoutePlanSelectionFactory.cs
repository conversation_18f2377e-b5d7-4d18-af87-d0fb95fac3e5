﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetStreetNameFromRoutePlanSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetStreetNameFromRoutePlan);
                db.AddInParameter(command, "@Terminal", DbType.Int32, addressTriages.Terminal);
                db.AddInParameter(command, "@streetName", DbType.String, addressTriages.StreetName);
                db.AddInParameter(command, "@postalcode", DbType.String, addressTriages.PostalCode);
                

            }
            return command;
        }

        #endregion Public Methods
    }
}