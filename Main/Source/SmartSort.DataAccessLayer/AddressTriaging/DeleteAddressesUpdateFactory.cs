﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Xml;
using System.Xml.Serialization;
using System.Data;
using System.Text;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class DeleteAddressesUpdateFactory : IUpdateFactory<AddressTriageListIdentity>
    {
        #region Public Methods

        public DbCommand ConstructUpdateCommand(Database db, AddressTriageListIdentity idList)
        {
            DbCommand command = null;
            if (null != db && null != idList)
            {
                command = db.GetStoredProcCommand(Constants.SP_DeleteRecordsInTriageQueue);


                XmlSerializer serializer = new XmlSerializer(typeof(AddressTriageListIdentity));
                StringBuilder sb = new StringBuilder();


                XmlWriterSettings ws = new XmlWriterSettings();
                ws.OmitXmlDeclaration = true;
                ws.CheckCharacters = false;
                ws.Indent = false;
                ws.CloseOutput = false;
                ws.Encoding = Encoding.Default;
                XmlWriter writer = XmlWriter.Create(sb, ws);
                XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
                ns.Add("", "");
                serializer.Serialize(writer, idList, ns);
                writer.Close();

                db.AddInParameter(command, "@Doc", DbType.Xml, sb.ToString());   
                db.AddOutParameter(command, "@Status", DbType.String, 10);
            }
            return command;
        }

        #endregion Public Methods
    }
}