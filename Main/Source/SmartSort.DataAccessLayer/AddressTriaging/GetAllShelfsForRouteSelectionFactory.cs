﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetAllShelfsForRouteSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db && null != addressTriages)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetAllShelfsForRoute);
                db.AddInParameter(command, "@Terminal", DbType.String, addressTriages.Terminal);
                db.AddInParameter(command, "@Route", DbType.String, addressTriages.Route);
            }
            return command;
        }

        #endregion Public Methods
    }
}