﻿using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetTriageReasonCodesSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
            DbCommand command = null;
            if (null != db)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetTriageReasonCodes);                
            }
            return command;
        }

        #endregion Public Methods
    }
}