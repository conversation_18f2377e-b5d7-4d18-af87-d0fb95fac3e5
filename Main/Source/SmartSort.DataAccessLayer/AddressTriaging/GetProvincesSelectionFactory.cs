﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetProvincesSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriages)
        {
             DbCommand command = null;
             if (null != db && null != addressTriages)
             {
                 command = db.GetStoredProcCommand(Constants.SP_GetProvinces);
                 db.AddInParameter(command, "@Terminal", DbType.Int32, addressTriages.Terminal);
             }
            return command;
        }

        #endregion Public Methods
    }
}