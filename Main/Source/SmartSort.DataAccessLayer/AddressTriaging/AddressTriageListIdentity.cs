﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.DataAccessLayer.AddressTriaging
{    
    [XmlRoot("TriageIdList")]
    public class AddressTriageListIdentity
    {
        [XmlArray("IdList")]
        [XmlArrayItem(typeof(AddressTriageQueueIdentity), ElementName = "Id")]
        public List<AddressTriageQueueIdentity> Ids { get; set; }
    }

    public class AddressTriageQueueIdentity
    {
        [XmlElement("Value")]
        public int Id { get; set; }

        public AddressTriageQueueIdentity() {}

        public AddressTriageQueueIdentity(int id)
        {
            Id = id;
        }
    }
}
