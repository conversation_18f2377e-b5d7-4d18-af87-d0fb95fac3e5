﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.AddressTriaging
{
    public class GetTriagePinDetailsSelectionFactory : ISelectionFactory<Business.Entities.AddressTriaging>
    {
        #region Public Methods

        public DbCommand ConstructSelectCommand(Database db, Business.Entities.AddressTriaging addressTriagePins)
        {
            DbCommand command = null;
            if (null != db && null != addressTriagePins)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetTriagPins);
                db.AddInParameter(command, "@TriageQueueID", DbType.Int32, addressTriagePins.TriageQueueID);
            }
            return command;
        }

        #endregion Public Methods
    }
}