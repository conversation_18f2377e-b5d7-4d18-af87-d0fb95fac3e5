﻿using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;

namespace SmartSort.DataAccessLayer.RoutePlan
{
    public class RoutePlansSelectionRepository : ISelectionFactory<RoutePlanIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, RoutePlanIdentity routePlan)
        {
            DbCommand command = null;
            if (null != db && null != routePlan)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetRoutePlans);
                db.AddInParameter(command, "@Terminal", DbType.String, routePlan.Terminal);                
            }
            return command;
        }
    }
}
