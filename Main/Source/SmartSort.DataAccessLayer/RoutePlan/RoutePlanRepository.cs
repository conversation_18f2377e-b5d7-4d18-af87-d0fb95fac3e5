﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using SmartSort.Business.Entities;
using SmartSort.Common;


namespace SmartSort.DataAccessLayer.RoutePlan
{
    public class RoutePlanRepository : Repository<RouteSortPlan>
    {
        public RoutePlanRepository(string databaseName) : base(databaseName) {}
       

        public List<RoutePlanShort> GetRoutePlans(string terminal)
        {
            ISelectionFactory<RoutePlanIdentity> selectionFactory = new RoutePlansSelectionRepository();
            RoutePlanIdentity routePlanIdentity = new RoutePlanIdentity(terminal, 0);
            DataTable dtSortPlan = Find(selectionFactory, routePlanIdentity);
            return dtSortPlan.DataTableToList<RoutePlanShort>();
        }
    }

    
}
