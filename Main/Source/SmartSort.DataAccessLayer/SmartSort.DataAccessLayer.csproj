﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{29384837-9F0A-4B52-B9CA-A6DF63BF817E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartSort.DataAccessLayer</RootNamespace>
    <AssemblyName>SmartSort.DataAccessLayer</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Data, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\EnterpriseLibrary.Data.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddressTriaging\AddressTriagingRepository.cs" />
    <Compile Include="AddressTriaging\DeleteAddressesUpdateFactory.cs" />
    <Compile Include="AddressTriaging\GetUnitNumberFromRoutePlanSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAddressFromRoutePlanByIdSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetCustomerNameFromRoutePlanSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAddressFromRoutePlanSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAddressSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAddressTriageSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAllRouteAndShelfFromRoutePlanSelectionFactroy.cs" />
    <Compile Include="AddressTriaging\GetAllRoutesSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetAllShelfsForRouteSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetProvincesSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetStreetDirectionSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetStreetNameFromRoutePlanSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetStreetSuffixSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetStreetTypesSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetTriagePinDetailsSelectionFactory.cs" />
    <Compile Include="AddressTriaging\GetTriageReasonCodesSelectionFactory.cs" />
    <Compile Include="AddressTriaging\AddressTriageListIdentity.cs" />
    <Compile Include="AddressTriaging\RouteAndSelfUpdateFactory.cs" />
    <Compile Include="AddressTriaging\TriageAddressSelectionFactory.cs" />
    <Compile Include="AddressTriaging\TriageLastTouchUpdateFactory.cs" />
    <Compile Include="CourierManifest\CloseRouteUpdateFactory.cs" />
    <Compile Include="CourierManifest\GetCourierManifestReportSelectionFactory.cs" />
    <Compile Include="CourierManifest\CourierManifestIdentity.cs" />
    <Compile Include="CourierManifest\CourierManifestRepository.cs" />
    <Compile Include="CourierManifest\GetCourierManifestSelectionFactory.cs" />
    <Compile Include="CourierManifest\GetPudroAndRoutesSelectionFactory.cs" />
    <Compile Include="Generic\IDeleteFactory.cs" />
    <Compile Include="Generic\IDomainObjectFactory.cs" />
    <Compile Include="Generic\IInsertFactory.cs" />
    <Compile Include="Generic\ISelectionFactory.cs" />
    <Compile Include="Generic\IUpdateFactory.cs" />
    <Compile Include="GlobalConfiguration\GetGlobalConfigSelectionFactory.cs" />
    <Compile Include="GlobalConfiguration\GlobalConfigurationRepository.cs" />
    <Compile Include="GlobalConfiguration\GlobalConfigurationUpdateFactory.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="Login\GetTerminalListSelectionFactory.cs" />
    <Compile Include="Login\GetUserInfoSelectionFactory.cs" />
    <Compile Include="Login\GetUserRolesSelectionFactory.cs" />
    <Compile Include="Login\LoginRepository.cs" />
    <Compile Include="Login\LoginIdentity.cs" />
    <Compile Include="Login\ValidateTerminalSelectonFactory.cs" />
    <Compile Include="Generic\Repository.cs" />
    <Compile Include="PinHistory\GetPinHistorySelectionFactory.cs" />
    <Compile Include="PinHistory\PinHistoryIdentity.cs" />
    <Compile Include="PinHistory\PinHistoryRepository.cs" />
    <Compile Include="RoutePlan\RoutePlansSelectionRepository.cs" />
    <Compile Include="RoutePlan\RoutePlanIdentity.cs" />
    <Compile Include="RoutePlan\RoutePlanRepository.cs" />
    <Compile Include="ShiftManagement\CheckAMShiftStart.cs" />
    <Compile Include="ShiftManagement\AutoStartShiftUpdateFactory.cs" />
    <Compile Include="ShiftManagement\ApplyPreviousMoveUpdateFactory.cs" />
    <Compile Include="ShiftManagement\GetPreviousMovesHistorySelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetAutoStartShiftSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetMoveHistorySelectionFactory.cs" />
    <Compile Include="ShiftManagement\MoveHistoryIdentity.cs" />
    <Compile Include="ShiftManagement\VehicleVolumeIdentity.cs" />
    <Compile Include="ShiftManagement\GetActualsAddressesByRouteShelfSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetWorkingDaySelectionFactory.cs" />
    <Compile Include="ShiftManagement\LoadBalanceRestoreSelectionFactory.cs" />
    <Compile Include="ShiftManagement\LoadBalanceUpdateFactory.cs" />
    <Compile Include="ShiftManagement\GetAddressesFromOtherShelvesSelectionFactory.cs" />
    <Compile Include="ShiftManagement\PreShiftLoadBalancingIdentity.cs" />
    <Compile Include="ShiftManagement\GetAddressesByRouteShelfSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetRouteShelfByTerminalSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetActiveSortPlanSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetShiftStatisticsDetailsSelectionFactory.cs" />
    <Compile Include="ShiftManagement\GetVehicleVolumeReportSelectionFactory.cs" />
    <Compile Include="ShiftManagement\ShiftManagementRepository.cs" />
    <Compile Include="ShiftManagement\GetInitiateAMShiftSelectionFactory.cs" />
    <Compile Include="ShiftManagement\ShiftManagementIdentity.cs" />
    <Compile Include="SortPlanManagement\SortPlanInfoUpdateFactory.cs" />
    <Compile Include="SortPlanManagement\GetSortPlanInfoSelectionFactory.cs" />
    <Compile Include="SortPlanManagement\GetActiveRoutePlanSelectionFactory.cs" />
    <Compile Include="SortPlanManagement\GetSortPlanForRoutePlanSelectionFactory.cs" />
    <Compile Include="SortPlanManagement\SortPlanManagementRepository.cs" />
    <Compile Include="SortPlan\GetActiveSortPlansForTerminalSelectionRepository.cs" />
    <Compile Include="SortPlan\GetAllValidSortPlansForTerminalSelectionRepository.cs" />
    <Compile Include="SortPlan\GetMaxEndDateSelectionFactory.cs" />
    <Compile Include="SortPlan\GetSortPlanScheduleDatesSelectionFactory.cs" />
    <Compile Include="SortPlan\RemoveSortPlanRecurrenceDeleteFactory.cs" />
    <Compile Include="SortPlan\SortPlanRecurrenceUpdateFactory.cs" />
    <Compile Include="SortPlan\SortPlanRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SortPlan\GetSortPlanForCalendarSelectionFactory.cs" />
    <Compile Include="SortPlan\SortPlanByMonthUpdateFactory.cs" />
    <Compile Include="SortPlan\SortPlanIdentity.cs" />
    <Compile Include="TerminalConfiguration\GetLineDetailsSelectionFactory.cs" />
    <Compile Include="TerminalConfiguration\GetPudroInfoSelectionFactory.cs" />
    <Compile Include="TerminalConfiguration\PudroInfoUpdateFactory.cs" />
    <Compile Include="TerminalConfiguration\SortingLineItemUpdateFactory.cs" />
    <Compile Include="TerminalConfiguration\SortingLineRepository.cs" />
    <Compile Include="TerminalConfiguration\TerminalConfigurationRepository.cs" />
    <Compile Include="UserManagement\SAPAccountUpdateFactory.cs" />
    <Compile Include="UserManagement\CreateUserUpdateFactory.cs" />
    <Compile Include="UserManagement\DisableUserUpdateFactory.cs" />
    <Compile Include="UserManagement\EditUserUpdateFactory.cs" />
    <Compile Include="UserManagement\GetUsersSelectionFactory.cs" />
    <Compile Include="UserManagement\GetAssignableRolesSelectionFactory.cs" />
    <Compile Include="UserManagement\GetUserTerminalsSelectionFactory.cs" />
    <Compile Include="UserManagement\UserManagementRepository.cs" />
    <Compile Include="UserProfile\GetOffsetByTerminalSelectionFactory.cs" />
    <Compile Include="UserProfile\GetProfileBySAPSelectionFactory.cs" />
    <Compile Include="UserProfile\GetProfileSelectionFactory.cs" />
    <Compile Include="UserProfile\SetProfileUpdateFactory.cs" />
    <Compile Include="UserProfile\UserProfileRepository.cs" />
    <Compile Include="XmlParser.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SmartSort.Business.Entities\SmartSort.Business.Entities.csproj">
      <Project>{5662ba8a-f770-420b-a01c-a388b211f25d}</Project>
      <Name>SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Business.Entities\SmartSort.Business.Entities.csproj">
      <Project>{5662ba8a-f770-420b-a01c-a388b211f25d}</Project>
      <Name>SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Common\SmartSort.Common.csproj">
      <Project>{4a28b5ff-887c-4a9a-b054-5e88f1119ab3}</Project>
      <Name>SmartSort.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="Packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>