﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.DataAccessLayer.PinHistory
{    
    [XmlRoot("PinHistory")]
    public class PinHistoryIdentity
    {
        [XmlArray("Pins")]
        [XmlArrayItem(typeof(PinIdentity), ElementName = "Pin")]
        public List<PinIdentity> Pins { get; set; }
    }

    public class PinIdentity
    {
        [XmlElement("P")]
        public string Pin { get; set; }

        public PinIdentity() {}

        public PinIdentity(string pin)
        {
            Pin = pin;
        }
    }
}
