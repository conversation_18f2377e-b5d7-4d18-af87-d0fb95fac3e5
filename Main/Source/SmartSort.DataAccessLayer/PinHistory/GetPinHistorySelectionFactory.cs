﻿using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using System.Data;
using System.Data.Common;
using Microsoft.Practices.EnterpriseLibrary.Data;
using SmartSort.Common;
namespace SmartSort.DataAccessLayer.PinHistory
{
    public class GetPinHistorySelectionFactory : ISelectionFactory<PinHistoryIdentity>
    {
        public DbCommand ConstructSelectCommand(Database db, PinHistoryIdentity pinId)
        {
            DbCommand command = null;
            if (null != db && null != pinId)
            {
                command = db.GetStoredProcCommand(Constants.SP_GetPinHistory);

                command.CommandTimeout = 240;

                XmlSerializer serializer = new XmlSerializer(typeof(PinHistoryIdentity));
                StringBuilder sb = new StringBuilder();


                XmlWriterSettings ws = new XmlWriterSettings();
                ws.OmitXmlDeclaration = true;
                ws.CheckCharacters = false;
                ws.Indent = false;
                ws.CloseOutput = false;
                ws.Encoding = Encoding.Default;
                XmlWriter writer = XmlWriter.Create(sb, ws);
                XmlSerializerNamespaces ns = new XmlSerializerNamespaces();
                ns.Add("", "");
                serializer.Serialize(writer, pinId, ns);
                writer.Close();


                //serializer.Serialize(Console.Out, p);


                db.AddInParameter(command, "@PinsXML", DbType.Xml, sb.ToString());
            }
            return command;
        }
    }
}
