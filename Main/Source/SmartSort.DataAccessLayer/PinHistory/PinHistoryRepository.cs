﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartSort.Business.Entities.PinHistory;
using System.Xml;
using System.Xml.Serialization;

namespace SmartSort.DataAccessLayer.PinHistory
{
    public class PinHistoryRepository: Repository<PinHistoryList>
    {
        public PinHistoryRepository(string dbName) : base(dbName) { }

        public PinHistoryList GetPinHistory(List<string> pins)
        {
            ISelectionFactory<PinHistoryIdentity> selectionFactory = new GetPinHistorySelectionFactory();

            PinHistoryIdentity identity = new PinHistoryIdentity();
            identity.Pins = pins.Select(p => new PinIdentity(p)).ToList();


            return base.FindOne(selectionFactory, new PinHistoryDomainFactory(), identity);

        }       

    }

    internal class PinHistoryDomainFactory : IDomainObjectFactory<PinHistoryList>
    {
        public PinHistoryList Construct(IDataReader reader)
        {
            PinHistoryList phList = new PinHistoryList();
            if (reader[0] != null)
            {
                try
                {
                    string xml = reader[0].ToString();
                    XmlSerializer serializer = new XmlSerializer(typeof(PinHistoryList));
                    XmlReader xmlReader = XmlReader.Create(new StringReader(xml));

                    phList = (PinHistoryList)serializer.Deserialize(xmlReader);
                }
                catch (Exception ex)
                {
                    // log
                }
            }
            return phList;
        }
    }

}
