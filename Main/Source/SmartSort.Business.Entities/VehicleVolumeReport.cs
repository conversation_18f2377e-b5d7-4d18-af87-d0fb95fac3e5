﻿using SmartSort.Common;
using System;
using System.Collections.Generic;
namespace SmartSort.Business.Entities
{
    public class VehicleVolumeReport : BaseEntity
    {  
        #region Public Properties
        public string Route { get; set; }
        public string Of { get; set; }
        public string Type { get; set; }
        public Int32 Total { get; set; }
        public Int32 CMTotal { get; set; }
        public int MR { get; set; }
        [DataColumnMetadataAttribute(Constants.ShelfContentsRegexTableColumName, false, true)]
        public Dictionary<string, int> ShelfContents { get; set; }
        #endregion
    }
}