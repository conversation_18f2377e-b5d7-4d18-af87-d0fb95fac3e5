﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.Business.Entities
{
    public class PreShiftLoadBalancing
    {
        #region Public Properties
        public string TerminalName { get; set; }
        public string UserName { get; set; }
        public string SourceRoute { get; set; }
        public string SourceShelf { get; set; }
        public string TargetRoute { get; set; }
        public string TargetShelf { get; set; }
        public bool IsMoveEntireShelf { get; set; }
        public List<CustomerStop> CustomerStops { get; set; }
        #endregion
    }
}
