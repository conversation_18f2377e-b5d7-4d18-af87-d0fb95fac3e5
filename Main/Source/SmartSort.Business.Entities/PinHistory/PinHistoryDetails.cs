﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.Business.Entities.PinHistory
{
    public class PinHistoryDetails
    {
        [XmlElement("PiecePin")]
        public string Pin { get; set; }

        [XmlElement("ShipmentManifest")]
        public PinHistoryShipmentManifest ShipmentManifestInfo { get; set; }

        [XmlElement("AddressTriageInfo")]
        public PinHistoryAddressTriageInfo AddressTriageInfo { get; set; }

        [XmlElement("ddressTriageQueue")]
        public PinHistoryAddressTriageQueue AddressTriageQueue { get; set; }        

        [XmlElement("SmartSortInfo")]
        public PinHistorySmartSortInfo SmartSortInfo { get; set; }

        [XmlElement("PDTInfo")]
        public PinHistoryPDTInfo PDTInfo { get; set; }

        [XmlElement("CourierManifest")]
        public PinHistoryCourierManifest CourierManifestInfo { get; set; }

        [XmlElement("PinMasterStateCodeLog")]
        public PinHistoryPinMasterLog PinMasterLog { get; set; }



        public object GetValue(Type entityType, string propertyName)
        {
            switch (entityType.Name)
            {
                case "PinHistoryShipmentManifest":
                    return ShipmentManifestInfo == null? string.Empty: ShipmentManifestInfo.GetFieldValue(propertyName);
                case "PinHistoryAddressTriageInfo":
                    return AddressTriageInfo == null? string.Empty: AddressTriageInfo.GetFieldValue(propertyName);
                case "PinHistoryAddressTriageQueue":
                    return AddressTriageQueue == null ? string.Empty : AddressTriageQueue.GetFieldValue(propertyName);
                case "PinHistorySmartSortInfo":
                    return SmartSortInfo == null? string.Empty: SmartSortInfo.GetFieldValue(propertyName);
                case "PinHistoryPDTInfo":
                    return PDTInfo == null? string.Empty: PDTInfo.GetFieldValue(propertyName);
                case "PinHistoryCourierManifest":
                    return CourierManifestInfo == null ? string.Empty : CourierManifestInfo.GetFieldValue(propertyName);
                case "PinHistoryPinMasterLog":
                    return PinMasterLog == null ? string.Empty : PinMasterLog.GetFieldValue(propertyName);
                default:
                    return string.Empty;
            }
        }
    }
}
