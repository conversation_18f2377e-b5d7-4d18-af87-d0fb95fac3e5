﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.Business.Entities.PinHistory
{
    public class PinHistoryCourierManifest : PinHistoryEntity
    {
        public string CMDownloadTime { get; set; }
        public string CMDownloadSource { get; set; }
        public string Route { get; set; }  
        public string CustomerName { get; set; }  
        public string UnitNumber { get; set; }  
        public string StreetNumber { get; set; }  
        public string StreetNumberSuf { get; set; }  
        public string StreetName { get; set; }  
        public string StreetType { get; set; }  
        public string StreetDirection { get; set; }  
        public string City { get; set; }  
        public string PostalCode { get; set; }  
        public string Shelf { get; set; }  
        public string DeliverySeqId { get; set; }  
        public string TruckShelfOverride { get; set; }  
        public string PackageType { get; set; }  
        public string CustomerStopID { get; set; }  
        public string DriverStopId { get; set; }          
    }
}
