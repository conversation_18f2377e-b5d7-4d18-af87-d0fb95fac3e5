﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.Business.Entities.PinHistory
{
    public class PinHistorySmartSortInfo : PinHistoryEntity
    {
        public string Terminal { get; set; }
        public string RoutePlanVersionID { get; set; }
        public string ParkingPlanCodeID { get; set; }
        public string PrimarySort { get; set; }
        public string BeltSide { get; set; }
        public string Route { get; set; }

        [XmlArray("ScanTimeReasons")]
        [XmlArrayItem(typeof(PinHistorySSReason), ElementName = "ScanTimeReason")]
        public PinHistorySSReason[] SSStatusReasons { get; set; } 

        public string CorrectedAddress { get; set; }
        public string SmartSortMode { get; set; }
        public string BarcodeType { get; set; }
        public string PackageType { get; set; }
        public string ShipmentType { get; set; }
        public string HandlingClassType { get; set; }
        public string DeliveryType { get; set; }
        public string ResolvedBy { get; set; }
        public string AlternateAddressFlag { get; set; }
        public string Shelf { get; set; }
        public string DeliveryTime { get; set; }
        public string DiversionCode { get; set; }
        public string DeliverySeqID { get; set; }
        public string TruckShelfOverride { get; set; }
        public string PreprintID { get; set; }
        
        public string UNUSED { get; set; }
    }
}
