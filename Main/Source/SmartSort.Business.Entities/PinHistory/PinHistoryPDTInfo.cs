﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.Business.Entities.PinHistory
{
    public class PinHistoryPDTInfo : PinHistoryEntity
    {

        public string PDTDeviceID { get; set; }

        [XmlArray("Actions")]
        [XmlArrayItem(typeof(PinHistoryPDTAction), ElementName = "Action")]
        public PinHistoryPDTAction[] Actions { get; set; } 

        public string RouteID { get; set; }
        public string Route { get; set; }
        public string Shelf { get; set; }
        public string TruckShelfOverride { get; set; }
    }
}
