﻿namespace SmartSort.Business.Entities
{
    public class AddressInfoByRoute : BaseEntity
    {
        #region Private variable

        #endregion Private variable

        #region Public Property

        public int Id { get; set; }

        public string StreetNumber { get; set; }

        public string StreetName { get; set; }

        public string StreetType { get; set; }

        public string StreetDirection { get; set; }

        public string PostalCode { get; set; }

        public string City { get; set; }

        public string CustomerName { get; set; }

        public string AddressLine { get; set; }

        public string ProvinceCode { get; set; }

        public string UnitNumber { get; set; }

        public string StreetNumberSuffix { get; set; }
        #endregion Public Property
    }
}