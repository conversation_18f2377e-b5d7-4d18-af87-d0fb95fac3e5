﻿using System;
using System.Collections.Generic;

namespace SmartSort.Business.Entities
{
    public class AddressDetails : BaseEntity
    {
        #region Internal Variables

        private readonly List<ListDetails> provinceList = null;
        private readonly List<ListDetails> suffixList = null;
        private readonly List<ListDetails> directionList = null;
        private readonly List<ListDetails> streetTypeList = null;
        private readonly List<ListDetails> changeResionList = null;

        #endregion Internal Variables

        #region Properties

        public string StreetNumber { get; set; }

        public string PostalCode { get; set; }

        public string StreetSuffix { get; set; }

        public string StreetName { get; set; }

        public string StreetType { get; set; }

        public string StreetDirection { get; set; }

        public string SuiteNumber { get; set; }

        public string EntryCode { get; set; }

        public string AddressLine2 { get; set; }

        public string AddressLine3 { get; set; }

        public string City { get; set; }

        public string Province { get; set; }

        public string ChangeReason { get; set; }

        public bool IsPostalCodeFoundInLookup { get; set; }

        public string CityProvincePostalCode { get; set; }

        public string Name { get; set; }

        public string PhoneNumber { get; set; }

        public string PhoneExtension { get; set; }

        public string EmailAddress { get; set; }

        public bool EmailIndicator { get; set; }

        public bool IsFirstPostalCodeLookup { get; set; }

        public List<ListDetails> ChangeRegionList
        {
            get { return changeResionList; }
            //set { changeResionList = value; }
        }

        public List<ListDetails> ProvinceList
        {
            get { return provinceList; }
            //set { provinceList = value; }
        }

        public List<ListDetails> SuffixList
        {
            get { return suffixList; }
            //set { suffixList = value; }
        }

        public List<ListDetails> DirectionList
        {
            get { return directionList; }
            //set { directionList = value; }
        }

        public List<ListDetails> StreetTypeList
        {
            get { return streetTypeList; }
            //set { streetTypeList = value; }
        }

        #endregion Properties

        public int TriageQueueID { get; set; }

        public string DeclareStreetNumber { get; set; }

        public string DeclareAddressLine1 { get; set; }

        public string DeclareAddressLine2 { get; set; }

        public string DeclareAddressLine3 { get; set; }

        public string DeclareCompanyName { get; set; }

        public string DeclareCity { get; set; }

        public string DeclareProvince { get; set; }

        public string DeclarePostalCode { get; set; }

        public string CurrentUnit { get; set; }

        public string CurrentStreetNum { get; set; }

        public string CurrentStreetNumSuf { get; set; }

        public string CurrentStreetName { get; set; }

        public string CurrentStreetType { get; set; }

        public string CurrentStreetDir { get; set; }

        public string CurrentCity { get; set; }

        public string CurrentProvince { get; set; }

        public string CurrentPostalCode { get; set; }

        public string CurrentCompanyName { get; set; }

        public string Pieces { get; set; }

        public string Terminal { get; set; }

        public string TriageReasonCode { get; set; }

        public string UserName { get; set; }

        public DateTimeOffset UpdateDateTimeOffset { get; set; }

        public string Route { get; set; }

        public string Shelf { get; set; }

        public string PrimarySort { get; set; }

        public string BeltSide { get; set; }

        public string AddressSource { get; set; }
    }
}