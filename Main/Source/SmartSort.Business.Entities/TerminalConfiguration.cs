﻿using System;
namespace SmartSort.Business.Entities
{
    public class TerminalConfiguration:BaseEntity
    {
        #region Public Properties
        public string CallStack { get; set; }
        public int TermConfID { get; set; }
        public string Terminal { get; set; }
        public int LineNumber { get; set; }
        public string PrimarySort { get; set; }
        public string LineDescription { get; set; }
        public int ActiveRoutesOnLeft { get; set; }
        public int ActiveRoutesOnRight { get; set; }
        public int ActiveRoutesOnCenter { get; set; }
        public int RoutesOnLeft { get; set; }
        public int RoutesOnCentre { get; set; }
        public int RoutesOnRight { get; set; }
        public string Active { get; set; }
        public string LastModifyBy { get; set; }
        public string Side { get; set; }
        public int Position { get; set; }
        public DateTime LastUpdateDateTime { get; set; }
        public string Status { get; set; }
        public int TotalRoutes { get; set; }
      
        #endregion
    }
}
