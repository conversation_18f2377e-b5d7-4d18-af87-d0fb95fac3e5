﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5662BA8A-F770-420B-A01C-A388B211F25D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartSort.Business.Entities</RootNamespace>
    <AssemblyName>SmartSort.Business.Entities</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddressCommonDetails.cs" />
    <Compile Include="AddressDetails.cs" />
    <Compile Include="AddressInfoByRoute.cs" />
    <Compile Include="AddressRequest.cs" />
    <Compile Include="AddressTriagePin.cs" />
    <Compile Include="AddressTriaging.cs" />
    <Compile Include="BaseEntity.cs" />
    <Compile Include="AutoStartShift.cs" />
    <Compile Include="ApplyPreviousMove.cs" />
    <Compile Include="GlobalConfiguration.cs" />
    <Compile Include="MoveDateHistory.cs" />
    <Compile Include="MoveHistory.cs" />
    <Compile Include="PinHistory\PinHistoryAddressTriageQueue.cs" />
    <Compile Include="PinHistory\PinHistoryPinMasterLog.cs" />
    <Compile Include="PinHistory\PinHistoryAddressTriageInfo.cs" />
    <Compile Include="PinHistory\PinHistoryCourierManifest.cs" />
    <Compile Include="PinHistory\PinHistoryDetails.cs" />
    <Compile Include="PinHistory\PinHistoryEntity.cs" />
    <Compile Include="PinHistory\PinHistoryList.cs" />
    <Compile Include="PinHistory\PinHistoryPDTAction.cs" />
    <Compile Include="PinHistory\PinHistoryPDTInfo.cs" />
    <Compile Include="PinHistory\PinHistoryShipmentManifest.cs" />
    <Compile Include="PinHistory\PinHistorySmartSortInfo.cs" />
    <Compile Include="PinHistory\PinHistorySSReason.cs" />
    <Compile Include="PinHistory\PinMasterStateCode.cs" />
    <Compile Include="RoutePlanShort.cs" />
    <Compile Include="CourierManifestSummaryItem.cs" />
    <Compile Include="CourierManifestSummary.cs" />
    <Compile Include="CourierManifest.cs" />
    <Compile Include="CourierManifestItem.cs" />
    <Compile Include="CustomerStop.cs" />
    <Compile Include="PreShiftLoadBalancing.cs" />
    <Compile Include="ShiftManagement.cs" />
    <Compile Include="ShiftStatus.cs" />
    <Compile Include="SortingLine.cs" />
    <Compile Include="SortingLineDetails.cs" />
    <Compile Include="SortPlanManagementItem.cs" />
    <Compile Include="PudroConfigurationItem.cs" />
    <Compile Include="RouteDetails.cs" />
    <Compile Include="CourierManifestSortPlanRoutesStatus.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="ListDetails.cs" />
    <Compile Include="TerminalConfiguration.cs" />
    <Compile Include="UserFunction.cs" />
    <Compile Include="ShiftStatistics.cs" />
    <Compile Include="RouteSortPlan.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Terminals.cs" />
    <Compile Include="UserInfo.cs" />
    <Compile Include="UserManagement.cs" />
    <Compile Include="UserProfileDetails.cs" />
    <Compile Include="VehicleVolumeReport.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SmartSort.Common\SmartSort.Common.csproj">
      <Project>{4a28b5ff-887c-4a9a-b054-5e88f1119ab3}</Project>
      <Name>SmartSort.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>