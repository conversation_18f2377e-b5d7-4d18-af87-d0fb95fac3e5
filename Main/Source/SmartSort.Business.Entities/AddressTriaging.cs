﻿using System;

namespace SmartSort.Business.Entities
{
    public class AddressTriaging : BaseEntity
    {
       #region Properties

        public DateTime EDD { get; set; }

        public string Customer { get; set; }

        public string AddressStatus { get; set; }

        public int Pieces { get; set; }

        public string LastUpdatedBy { get; set; }

        public DateTime LastUpdatedOn { get; set; }

        public int AddressID { get; set; }

        public string Address { get; set; }

        public string TriageReasonCode { get; set; }

        public string TriageDescription { get; set; }

        public string TriageDescriptionFR { get; set; }

        public string StreetName { get; set; }

        public string StreetType { get; set; }

        public string StreetDirection { get; set; }

        public string PostalCode { get; set; }

        public string City { get; set; }

        public string ProvinceCode { get; set; }

        public string CustomerName { get; set; }

        public string AddressLine { get; set; }

        public int TriageQueueID { get; set; }

        public string DeclareStreetNumber { get; set; }

        public string DeclareAddressLine1 { get; set; }

        public string DeclareAddressLine2 { get; set; }

        public string DeclareAddressLine3 { get; set; }

        public string DeclareCompanyName { get; set; }

        public string DeclareCity { get; set; }

        public string DeclareProvince { get; set; }

        public string DeclarePostalCode { get; set; }

        public DateTime ExpectedDeliveryDate { get; set; }

        public DateTimeOffset ? LastUpdateDateTime { get; set; }

        public string LastModifyBy { get; set; }

        public string PurolatorAVStatus { get; set; }

        public string Terminal { get; set; }

        public string EscalatedAddressOnly { get; set; }

        public int PageNumber { get; set; }

        public int RowsPerPage { get; set; }

        public int NoOfAddresses { get; set; }

        public string SortColumn { get; set; }

        public string Route { get; set; }

        public string Shelf { get; set; }

        public string UserName { get; set; }

        public string Status { get; set; }

        public string StreetNumSuf { get; set; }

        public string Suite { get; set; }

        public DateTimeOffset UpdateDateTimeOffset { get; set; }

        public string PrimarySort { get; set; }

        public string BeltSide { get; set; }

       

        #endregion Properties
    }
}