﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace SmartSort.Business.Entities
{
    public class CustomerStop
    {
        #region Public Properties
        [XmlIgnore]
        public bool IsMoveRequired { get; set; }
        public string SourceShelf { get; set; }
        public string TargetShelf { get; set; }
        public int Pieces { get; set; }
        public string CustomerNames { get; set; }
        public string UnitNumber { get; set; }
        public string StreetNumber { get; set; }
        public string StreetNumSuf { get; set; }
        public string StreetName { get; set; }
        public string StreetType { get; set; }
        public string StreetDirection { get; set; }
        public string City { get; set; }
        public string Province { get; set; }
        public string PostalCode { get; set; }
        public bool IsGrouped { get; set; }
        #endregion
    }
}
