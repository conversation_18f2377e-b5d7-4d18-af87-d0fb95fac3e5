﻿namespace SmartSort.Business.Entities
{
    public class MoveHistory : BaseEntity
    {        
        public string MoveType { get; set; }
        public string MovedBy { get; set; }
        public string MovingTime { get; set; }
        public string FromRoute { get; set; }
        public string FromShelf { get; set; }
        public string FromTSO { get; set; }
        public string ToRoute { get; set; }
        public string ToShelf { get; set; }
        public string ToTSO { get; set; }
        public string UnitNumber { get; set; }
        public string StreetNumber { get; set; }
        public string StreetNumSuf { get; set; }
        public string StreetName { get; set; }
        public string StreetType { get; set; }
        public string StreetDirection { get; set; }
        public string PostalCode { get; set; }
        public string City { get; set; }
        public string Province { get; set; }
        public int Stops { get; set; }
        public int Pieces { get; set; }
    }
}