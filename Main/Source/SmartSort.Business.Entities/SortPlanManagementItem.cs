﻿using System;
using System.Collections.Generic;
using SmartSort.Common;

namespace SmartSort.Business.Entities
{
    public class SortPlanManagementItem
    {
        #region Private Variables
        private List<PudroConfigurationItem> pudroConfigrationItems = new List<PudroConfigurationItem>();
        #endregion

        #region Public Properties
        public string UserName { get; set; }
        public string TerminalName { get; set; }
        public int SortPlanId { get; set; }

        [DataColumnMetadata(Constants.RoutePlanCodeIdTableColumnName)]
        public int RoutePlanCodeId { get; set; }

        [DataColumnMetadata(Constants.ParkingPlanCodeIdTableColumnName)]
        public int ParkingPlanCodeId { get; set; }

        [DataColumnMetadata(Constants.RoutePlanCodeTableColumnName)]
        public string RoutePlanName { get; set; }

        [DataColumnMetadata(Constants.SortPlanCodeTableColumnName)]
        public string SortPlanName { get; set; }
        
        [DataColumnMetadata(Constants.SortPlanDescriptionTableColumnName)]
        public string SortPlanDescription { get; set; }

        [DataColumnMetadata(Constants.SortPlanDefaultTableColumnName, true)]
        public bool IsDefaultSortPlan { get; set; }

        [DataColumnMetadata(Constants.IsDefaultSortPlanPresentTableColumnName, true)]
        public bool IsDefaultSortPlanPresent { get; set; }

        [DataColumnMetadata(Constants.ParkingPlanCodeTableColumnName)]
        public string ParkingPlanName { get; set; }

        [DataColumnMetadata(Constants.ParkingPlanDescriptionTableColumnName)]
        public string ParkingPlanDescription { get; set; }

        [DataColumnMetadata(Constants.LastUpdateDateTimeTableColumnName)]
        public DateTime ParkingPlanLastUpdateDateTime { get; set; }

        [DataColumnMetadata(Constants.AllRoutesTableColumnName)]
        public List<string> Routes { get; set; }

        public List<string> AssignedRoutes { get; set; }
        public List<string> UnassignedRoutes { get; set; }

        [DataColumnMetadata(Constants.IsSortPlanEditable, true)]
        public bool IsSortPlanEditable { get; set; }

        public int RouteCount
        {
            get { return Routes != null ? Routes.Count : 0; }
        }

        public int AssignedRouteCount
        {
            get { return AssignedRoutes != null ? AssignedRoutes.Count : 0; }
        }

        public int UnassignedRouteCount
        {
            get { return UnassignedRoutes != null ? UnassignedRoutes.Count : 0; }
        }

        public List<PudroConfigurationItem> PudroConfigrationItems
        {
            get { return pudroConfigrationItems; }
        }
        #endregion
    }
}
