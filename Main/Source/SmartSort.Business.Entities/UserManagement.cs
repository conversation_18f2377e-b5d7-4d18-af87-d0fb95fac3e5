﻿using System.Collections.Generic;

namespace SmartSort.Business.Entities
{
    public class UserManagement : BaseEntity
    {
        #region Public Properties

        private readonly List<UserProfileDetails> userProfileDetails = new List<UserProfileDetails>();

        public List<UserProfileDetails> UserProfiles
        {
            get { return userProfileDetails; }
        }

        #endregion Public Properties
    }
}