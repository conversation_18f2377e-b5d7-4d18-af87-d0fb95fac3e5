﻿using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using SmartSort.Common;

namespace SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "PudroConfigurationItem"), XmlType("PudroConfigurationItem")]
    public class PudroConfigurationItem : BaseEntity
    {
        #region Private Variables
        private TerminalConfiguration pudroDetails;
        private List<RouteDetails> allRoutes = new List<RouteDetails>();
        private SortedDictionary<int, RouteDetails> leftRoutes = new SortedDictionary<int, RouteDetails>();
        private SortedDictionary<int, RouteDetails> centerRoutes = new SortedDictionary<int, RouteDetails>();
        private SortedDictionary<int, RouteDetails> rightRoutes = new SortedDictionary<int, RouteDetails>();
        #endregion

        #region Public Properties
        [XmlIgnore]
        public TerminalConfiguration PudroDetails
        {
            get { return pudroDetails; }
        }

        [XmlIgnore]
        public int LeftRouteCount
        {
            get { return pudroDetails != null ? pudroDetails.RoutesOnLeft : 0; }
        }

        [XmlIgnore]
        public int CenterRouteCount
        {
            get { return pudroDetails != null ? pudroDetails.RoutesOnCentre : 0; }
        }

        [XmlIgnore]
        public int RightRouteCount
        {
            get { return pudroDetails != null ? pudroDetails.RoutesOnRight : 0; }
        }

        [XmlIgnore]
        public int MaxLeftRightRouteCount
        {
            get { return Math.Max(LeftRouteCount, RightRouteCount); }
        }

        [XmlIgnore]
        public SortedDictionary<int, RouteDetails> LeftRoutes
        {
            get { return leftRoutes; }
        }

        [XmlIgnore]
        public SortedDictionary<int, RouteDetails> CenterRoutes
        {
            get { return centerRoutes; }
        }

        [XmlIgnore]
        public SortedDictionary<int, RouteDetails> RightRoutes
        {
            get { return rightRoutes; }
        }

        public List<RouteDetails> AllRoutes
        {
            get { return allRoutes; }
        }
        #endregion

        #region Public Constructors
        public PudroConfigurationItem()
        {
        }

        public PudroConfigurationItem(TerminalConfiguration pudroDetails)
        {
            this.pudroDetails = pudroDetails;
        }
        #endregion

        #region Private Methods
        private void BuildRoutes(SortedDictionary<int, RouteDetails> routes, int routePosition, string beltSide, bool isEnabled)
        {
            if (!routes.ContainsKey(routePosition))
            {
                routes.Add(routePosition, new RouteDetails()
                {
                    TerminalConfigurationId = 0,
                    RouteName = String.Empty,
                    VehicleName = String.Empty,
                    BeltSide = beltSide,
                    RoutePosition = routePosition,
                    IsPlaceholder = !isEnabled
                });
            }
        }
        #endregion

        #region Public Methods
        public void BuildMissingRoutePositions()
        {
            for (int i = 1; i <= MaxLeftRightRouteCount; i++)
            {
                BuildRoutes(LeftRoutes, i, Constants.LeftBeltSideDbValue, i <= LeftRouteCount);
                BuildRoutes(RightRoutes, i, Constants.RightBeltSideDbValue, i <= RightRouteCount);
            }

            for (int i = 1; i <= CenterRouteCount; i++)
            {
                BuildRoutes(CenterRoutes, i, Constants.CenterBeltSideDbValue, true);
            }
        }
        #endregion
    }
}
