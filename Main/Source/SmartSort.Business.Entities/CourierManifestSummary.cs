﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.Business.Entities
{
    public class CourierManifestSummary
    {
        #region Public Properties
        public string Route { get; set; }
        public string TerminalName { get; set; }
        public int TotalVehicleStops { get; set; }
        public int TotalCustomerStops { get; set; }
        public int TotalPieces { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime ManifestedDate { get; set; }        
        public List<CourierManifestSummaryItem> SummaryItems { get; set; }
        #endregion
    }
}
