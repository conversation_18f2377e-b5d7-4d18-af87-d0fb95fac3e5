﻿using System.Xml.Serialization;

namespace SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "UserInfo")]
    public class UserInfo : BaseEntity
    {
        #region Public Properties
        [XmlElement(ElementName = "UserName")]
        public string UserName { get; set; }

        [XmlElement(ElementName = "SAPUserID")]
        public string SapUserId { get; set; }

        [XmlElement(ElementName = "RoleName")]
        public string RoleName { get; set; }

        [XmlElement(ElementName = "LastModifyBy")]
        public string LastModifyBy { get; set; }

        [XmlElement(ElementName = "LastUpdateDateTime")]
        public string LastUpdateDateTime { get; set; }

        [XmlElement(ElementName = "Userid")]
        public int UserId { get; set; }

        [XmlElement(ElementName = "FirstName")]
        public string FirstName { get; set; }

        [XmlElement(ElementName = "LastName")]
        public string LastName { get; set; }

        [XmlElement(ElementName = "FullName")]
        public string FullName { get; set; }

        [XmlElement(ElementName = "Language")]
        public string Language { get; set; }

        [XmlElement(ElementName = "Email")]
        public string Email { get; set; }

        [XmlElement(ElementName = "ReceiveEmailNotIf")]
        public string ReceiveEmailNotIf { get; set; }

        [XmlElement(ElementName = "UserProfileStatus")]
        public string UserProfileStatus { get; set; }

        #endregion
    }
}