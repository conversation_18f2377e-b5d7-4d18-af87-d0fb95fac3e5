﻿using SmartSort.Common;
using System;
using System.Collections.Generic;
namespace SmartSort.Business.Entities
{
    public class ShiftManagement : BaseEntity
    {  
        #region Public Properties
        public List<VehicleVolumeReport> VehicleVolumeReports { get; set; }
        public List<MoveDateHistory> PreviousMoveDates { get; set; }
        public DateTime ShiftDate { get; set; }
        public string AutoStartTime { get; set; }
        public string TerminalName { get; set; }
        public string SortPlanName { get; set; }
        public bool IsShiftStarted { get; set; }
        public int TotalProjectedStops { get; set; }
        public int TotalProjectedPieces { get; set; }
        public int TotalActualStops { get; set; }
        public int TotalActualPieces { get; set; }
        public int TotalMRStops { get; set; }
        public int TotalMRPieces { get; set; }
        public int PinResetPieces { get; set; }
        #endregion
    }
}