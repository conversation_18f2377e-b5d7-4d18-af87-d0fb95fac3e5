﻿using System.Xml.Serialization;

namespace SmartSort.Business.Entities
{
    public class RouteDetails
    {
        #region Public Properties
        public int TerminalConfigurationId { get; set; }

        [XmlIgnore]
        public bool IsPlaceholder { get; set; }

        [XmlIgnore]
        public bool IsActive { get; set; }

        [XmlIgnore]
        public int RoutePosition { get; set; } 
        
        public string RouteName { get; set; } 
        
        public string VehicleName { get; set; }
        
        [XmlIgnore]
        public string BeltSide { get; set; }
        #endregion
    }
}