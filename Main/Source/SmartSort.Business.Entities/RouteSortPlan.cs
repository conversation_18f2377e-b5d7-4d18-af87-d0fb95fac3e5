﻿using System;

namespace SmartSort.Business.Entities
{
    public class RouteSortPlan : BaseEntity
    {
        #region Public Properties
        public int SortPlanId { get; set; }

        public string SortPlanCode { get; set; }

        public string SortPlanDescription { get; set; }

        public string SortPlanName { get; set; }

        public string SortPlanDefault { get; set; }

        public string ScheduleType { get; set; }

        public DateTime ScheduleStartDate { get; set; }

        public DateTime ScheduleEndDate { get; set; }

        public string ScheduleStartDateString { get; set; }

        public string ScheduleEndDateString { get; set; }

        public int ScheduleParamType { get; set; }

        public string ScheduleParam1 { get; set; }

        public string ScheduleParam2 { get; set; }

        public int SortplanScheduleID { get; set; }

        public string DayOfWeek { get; set; }

        public string AssignmentType { get; set; }

        public int UnAssignedRoutes { get; set; }

        public bool IsActive { get; set; }

        public DateTime Date { get; set; }

        public string TerminalName { get; set; }

        public int Month { get; set; }

        public int Year { get; set; }

        public string UserName { get; set; }

        public string DateString { get; set; }

        public int TotalRoutes { get; set; }

        public int RoutePlanCodeId { get; set; }

        public string RoutePlanCode { get; set; }

        public string RoutePlanDescription { get; set; }

        public string RoutePlanDefault { get; set; }

        public string InUseFlag { get; set; }

        public string Terminal { get; set; }

        public int SortPlanCalId { get; set; }

        public bool IsSortPlanEditable { get; set; }
        #endregion
    }
}