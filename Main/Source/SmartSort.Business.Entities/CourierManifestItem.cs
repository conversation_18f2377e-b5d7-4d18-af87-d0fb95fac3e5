﻿using SmartSort.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.Business.Entities
{
    public class CourierManifestItem
    {
        #region Public Properties
        [DataColumnMetadata(Constants.IsPRGroupingTableColumnName, true)]
        public bool IsPRGrouping { get; set; }
        //public int PRGroup { get; set; }
        public int DeliverySequenceId { get; set; }
        public string Shelf { get; set; }
        public string PackageLocation { get; set; }
        public string CustomerName { get; set; }
        public string ServiceTime { get; set; }
        public string StreetNumber { get; set; }
        public string Street { get; set; }
        public string UnitNumber { get; set; }
        public string City { get; set; }
        public Int16 Pieces { get; set; }
        public Int16 Mail { get; set; }
        public Int16 Boxes { get; set; }
        public Int16 Unclassified { get; set; }
        #endregion
    }
}
