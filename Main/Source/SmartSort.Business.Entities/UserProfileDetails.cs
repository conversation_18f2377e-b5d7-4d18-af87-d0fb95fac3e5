﻿using System.Xml.Serialization;

namespace SmartSort.Business.Entities
{
    [XmlRoot(ElementName = "UserProfile"), XmlType("UserProfile")]
    public class UserProfileDetails : BaseEntity
    {
        #region Public Properties
        public UserInfo UserInfo { get; set; }

        public Terminals Terminals { get; set; }

        public UserFunction Functions { get; set; }

        public string CurrentUserName { get; set; }
        public int CurrentUserId { get; set; }
        #endregion
    }
}