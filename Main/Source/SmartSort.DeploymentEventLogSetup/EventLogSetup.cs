﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration.Install;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;
using SmartSort.Common;

namespace SmartSort.DeploymentEventLogSetup
{
    [RunInstaller(true)]
    public class EventLogSetup : Installer
    {
        private EventLogInstaller myEventLogInstaller;
        private const string SOURCE_PREFIX = "SmartSort - ";

        public EventLogSetup()
        {
            //Create Instance of EventLogInstaller
            myEventLogInstaller = new EventLogInstaller();

            // Set the Source of Event Log, to be created.
            myEventLogInstaller.Source = SOURCE_PREFIX + Constants.LOGGING_CATEGORY_BUSINESS;

            // Set the Log that source is created in
            myEventLogInstaller.Log = "SmartSort";

            // Add myEventLogInstaller to the Installers Collection.
            Installers.Add(myEventLogInstaller);

            //Create Instance of EventLogInstaller
            myEventLogInstaller = new EventLogInstaller();

            // Set the Source of Event Log, to be created.
            myEventLogInstaller.Source = SOURCE_PREFIX + Constants.LOGGING_CATEGORY_CLIENT_INTEGRATION;

            // Set the Log that source is created in
            myEventLogInstaller.Log = "SmartSort";

            // Add myEventLogInstaller to the Installers Collection.
            Installers.Add(myEventLogInstaller);

            //Create Instance of EventLogInstaller
            myEventLogInstaller = new EventLogInstaller();

            // Set the Source of Event Log, to be created.
            myEventLogInstaller.Source = SOURCE_PREFIX + Constants.LOGGING_CATEGORY_DATA_ACCESS;

            // Set the Log that source is created in
            myEventLogInstaller.Log = "SmartSort";

            // Add myEventLogInstaller to the Installers Collection.
            Installers.Add(myEventLogInstaller);

            //Create Instance of EventLogInstaller
            myEventLogInstaller = new EventLogInstaller();

            // Set the Source of Event Log, to be created.
            myEventLogInstaller.Source = SOURCE_PREFIX + Constants.LOGGING_CATEGORY_USER_INTERFACE;

            // Set the Log that source is created in
            myEventLogInstaller.Log = "SmartSort";

            // Add myEventLogInstaller to the Installers Collection.
            Installers.Add(myEventLogInstaller);

            //Create Instance of EventLogInstaller
            myEventLogInstaller = new EventLogInstaller();

            // Set the Source of Event Log, to be created.
            myEventLogInstaller.Source = SOURCE_PREFIX + Constants.LOGGING_CATEGORY_GENERAL;

            // Set the Log that source is created in
            myEventLogInstaller.Log = "SmartSort";

            // Add myEventLogInstaller to the Installers Collection.
            Installers.Add(myEventLogInstaller);
        }
    }
}
