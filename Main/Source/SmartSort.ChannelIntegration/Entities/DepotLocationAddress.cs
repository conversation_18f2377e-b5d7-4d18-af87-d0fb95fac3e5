﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SmartSort.ChannelIntegration.Entities
{
    public class DepotLocationAddress
    {
        private string addressTypeField;
        
        private string cityCodeField;
        
        private string countryCodeField;
        
        private string postalCodeField;
        
        private string provinceCodeField;
        
        private string cityField;
        
        private string streetNumberField;
        
        private string streetSuffixField;
        
        private string streetNameField;
        
        private string streetTypeField;
        
        private string streetDirectionField;
        
        private string floorField;
        
        private string suiteField;
        
        private string entryCodeField;
        
        private string address2Field;
        
        private string address3Field;
        
        /// <remarks/>
        public string AddressType {
            get {
                return this.addressTypeField;
            }
            set {
                this.addressTypeField = value;
            }
        }
        
        /// <remarks/>
        public string CityCode {
            get {
                return this.cityCodeField;
            }
            set {
                this.cityCodeField = value;
            }
        }
        
        /// <remarks/>
        public string CountryCode {
            get {
                return this.countryCodeField;
            }
            set {
                this.countryCodeField = value;
            }
        }
        
        /// <remarks/>
        public string PostalCode {
            get {
                return this.postalCodeField;
            }
            set {
                this.postalCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ProvinceCode {
            get {
                return this.provinceCodeField;
            }
            set {
                this.provinceCodeField = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string StreetNumber {
            get {
                return this.streetNumberField;
            }
            set {
                this.streetNumberField = value;
            }
        }
        
        /// <remarks/>
        public string StreetSuffix {
            get {
                return this.streetSuffixField;
            }
            set {
                this.streetSuffixField = value;
            }
        }
        
        /// <remarks/>
        public string StreetName {
            get {
                return this.streetNameField;
            }
            set {
                this.streetNameField = value;
            }
        }
        
        /// <remarks/>
        public string StreetType {
            get {
                return this.streetTypeField;
            }
            set {
                this.streetTypeField = value;
            }
        }
        
        /// <remarks/>
        public string StreetDirection {
            get {
                return this.streetDirectionField;
            }
            set {
                this.streetDirectionField = value;
            }
        }
        
        /// <remarks/>
        public string Floor {
            get {
                return this.floorField;
            }
            set {
                this.floorField = value;
            }
        }
        
        /// <remarks/>
        public string Suite {
            get {
                return this.suiteField;
            }
            set {
                this.suiteField = value;
            }
        }
        
        /// <remarks/>
        public string EntryCode {
            get {
                return this.entryCodeField;
            }
            set {
                this.entryCodeField = value;
            }
        }
        
        /// <remarks/>
        public string Address2 {
            get {
                return this.address2Field;
            }
            set {
                this.address2Field = value;
            }
        }
        
        /// <remarks/>
        public string Address3
        {
            get
            {
                return this.address3Field;
            }
            set
            {
                this.address3Field = value;
            }
        }
    }
}
