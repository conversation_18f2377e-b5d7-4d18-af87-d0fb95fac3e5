﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace SmartSort.ChannelIntegration.Entities
{
    public class Address
    {
        private string _name;
        private string _phoneNumber;
        private string _phoneExtension;
        private string _postalCode;
        private string _streetNumber;
        private string _streetName;
        private string _streetType;
        private string _streetDirection;
        private string _streetSuffix;
        private string _entryCode;
        private string _suiteNumber;
        private string _addressLine2;
        private string _emailAddress;
        private bool _emailInd;
        private string _city;
        private string _provinceCode;
        private string _countryCode;

        public Address()
        {
        }

        public string CountryCode
        {
            get { return _countryCode; }
            set { _countryCode = value; }
        }

        public string ProvinceCode
        {
            get { return _provinceCode; }
            set { _provinceCode = value; }
        }

        public string City
        {
            get { return _city; }
            set { _city = value; }
        }

        public string Name
        {
            get { return _name; }
            set { _name = value; }
        }

        public string PhoneNumber
        {
            get { return _phoneNumber; }
            set { _phoneNumber = value; }
        }

        public string PhoneExtension
        {
            get { return _phoneExtension; }
            set { _phoneExtension = value; }
        }

        public string PostalCode
        {
            get { return _postalCode; }
            set { _postalCode = value; }
        }

        public string StreetNumber
        {
            get { return _streetNumber; }
            set { _streetNumber = value; }
        }

        public string StreetName
        {
            get { return _streetName; }
            set { _streetName = value; }
        }

        public string StreetType
        {
            get { return _streetType; }
            set { _streetType = value; }
        }

        public string StreetDirection
        {
            get { return _streetDirection; }
            set { _streetDirection = value; }
        }

        public string SuiteNumber
        {
            get { return _suiteNumber; }
            set { _suiteNumber = value; }
        }

        public string AddressLine2
        {
            get { return _addressLine2; }
            set { _addressLine2 = value; }
        }

        public string EmailAddress
        {
            get { return _emailAddress; }
            set { _emailAddress = value; }
        }

        public bool EmailInd
        {
            get { return _emailInd; }
            set { _emailInd = value; }
        }

        public string StreetSuffix
        {
            get { return _streetSuffix; }
            set { _streetSuffix = value; }
        }

        public string EntryCode
        {
            get { return _entryCode; }
            set { _entryCode = value; }
        }

        public bool IsCanadianAddress()
        {
            return CountryCode != null && CountryCode.Equals(SmartSort.Common.Constants.COUNTRY_CANADA_CODE);
        }

        public Address Clone()
        {
            Address newAddress = new Address();
            newAddress.Name = Name;
            newAddress.PhoneNumber = PhoneNumber;
            newAddress.PhoneExtension = PhoneExtension;
            newAddress.PostalCode = PostalCode;
            newAddress.StreetNumber = StreetNumber;
            newAddress.StreetName = StreetName;
            newAddress.StreetType = StreetType;
            newAddress.StreetDirection = StreetDirection;
            newAddress.StreetSuffix = StreetSuffix;
            newAddress.EntryCode = EntryCode;
            newAddress.SuiteNumber = SuiteNumber;
            newAddress.AddressLine2 = AddressLine2;
            newAddress.City = City;
            newAddress.ProvinceCode = ProvinceCode;
            newAddress.CountryCode = CountryCode;
            newAddress.EmailAddress = EmailAddress;
            newAddress.EmailInd = EmailInd;
            return newAddress;
        }

    }
}