﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SmartSort.ChannelIntegration.Entities
{
    public class SuggestedAddress : Address
    {
        public enum StreetNumberSequence
        {
            Odd,
            Even,
            All
        }

        public enum AddressTypes
        {
            Unknown,
            Regular,
            PostOfficeBox,
            RuralRoute,
            GeneralDelivery,
            Mixed,      //Not Actual Type:  Only used when a Postal Code has combination of 'Regular' and 'PO or RR or GD'
            Special     //Not Actual Type:  Only used when a Postal Code has combination of 'PO or RR or GD'
        }

        new public SuggestedAddress Clone()
        {
            SuggestedAddress newAddress = new SuggestedAddress();
            newAddress.AddressLine2 = AddressLine2;
            newAddress.City = City;
            newAddress.CountryCode = CountryCode;
            newAddress.FromStreetNumber = FromStreetNumber;
            newAddress.FromStreetNumberSuffix = FromStreetNumberSuffix;
            newAddress.PostalCode = PostalCode;
            newAddress.ProvinceCode = ProvinceCode;
            newAddress.StreetName = StreetName;
            newAddress.StreetSuffix = StreetSuffix;
            newAddress.EntryCode = EntryCode;
            newAddress.StreetNumber = StreetNumber;
            newAddress.StreetNumberSequenceType = StreetNumberSequenceType;
            newAddress.StreetType = StreetType;
            newAddress.StreetDirection = StreetDirection;
            newAddress.SuiteNumber = SuiteNumber;
            newAddress.ToStreetNumber = ToStreetNumber;
            newAddress.ToStreetNumberSuffix = ToStreetNumberSuffix;
            newAddress.AddressType = AddressType;
            return newAddress;
        }

        private string _fromStreetNumber;

        public string FromStreetNumber
        {
            get { return _fromStreetNumber; }
            set { _fromStreetNumber = value; }
        }

        private string _toStreetNumber;

        public string ToStreetNumber
        {
            get { return _toStreetNumber; }
            set { _toStreetNumber = value; }
        }

        private StreetNumberSequence _streetNumberSequenceType;

        public StreetNumberSequence StreetNumberSequenceType
        {
            get { return _streetNumberSequenceType; }
            set { _streetNumberSequenceType = value; }
        }

        private string _fromStreetNumberSuffix;

        public string FromStreetNumberSuffix
        {
            get { return _fromStreetNumberSuffix; }
            set { _fromStreetNumberSuffix = value; }
        }

        private string _toStreetNumberSuffix;

        public string ToStreetNumberSuffix
        {
            get { return _toStreetNumberSuffix; }
            set { _toStreetNumberSuffix = value; }
        }

        private AddressTypes _addressType = AddressTypes.Regular;

        public AddressTypes AddressType
        {
            get { return _addressType; }
            set { _addressType = value; }
        }

        public bool IsKludgedSuggestedAddressFromServiceDirectory()
        {
            bool retVal = false;
            if (!string.IsNullOrEmpty(CountryCode) &&
                !string.IsNullOrEmpty(ProvinceCode) &&
                !string.IsNullOrEmpty(PostalCode) &&
                !string.IsNullOrEmpty(City))
            {
                if (    string.IsNullOrEmpty(AddressLine2) &&
                        string.IsNullOrEmpty(FromStreetNumber) &&
                        string.IsNullOrEmpty(FromStreetNumberSuffix) &&
                        string.IsNullOrEmpty(StreetName) &&
                        string.IsNullOrEmpty(StreetNumber) &&
                        string.IsNullOrEmpty(StreetType) &&
                        string.IsNullOrEmpty(SuiteNumber) &&
                        string.IsNullOrEmpty(ToStreetNumber) &&
                        string.IsNullOrEmpty(ToStreetNumberSuffix)  )
                {
                    retVal = true;
                }
            }
            return retVal;
        }

    }
}

