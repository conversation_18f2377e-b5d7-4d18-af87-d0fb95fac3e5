﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using SmartSort.Common;

namespace SmartSort.ChannelIntegration.Entities
{
    public class DepotLocation
    {
        private string locationNumberField;

        private string locationTypeField;

        private string locationNameField;

        private DepotLocationAddress addressField;

        private string contactNameField;

        private string phoneNumberField;

        private string specialInstructionsEnField;

        private string specialInstructionsFrField;

        private string depotField;

        private string unicodeField;

        private bool holdForPickupField;

        private bool dangerousGoodsField;

        private bool kioskField;

        private bool streetAccessField;

        private bool wheelChairAccessField;

        private string openMonField;

        private string closeMonField;

        private string openExceptionMonField;

        private string closeExceptionMonField;

        private string openTueField;

        private string closeTueField;

        private string openExceptionTueField;

        private string closeExceptionTueField;

        private string openWedField;

        private string closeWedField;

        private string openExceptionWedField;

        private string closeExceptionWedField;

        private string openThuField;

        private string closeThuField;

        private string openExceptionThuField;

        private string closeExceptionThuField;

        private string openFriField;

        private string closeFriField;

        private string openExceptionFriField;

        private string closeExceptionFriField;

        private string openSatField;

        private string closeSatField;

        private string openExceptionSatField;

        private string closeExceptionSatField;

        private string openSunField;

        private string closeSunField;

        private string openExceptionSunField;

        private string closeExceptionSunField;

        private string dropOffWeekDayAirDomField;

        private string dropOffWeekDayAirUSField;

        private string dropOffWeekDayAirIntlField;

        private string dropOffWeekDayGndDomField;

        private string dropOffWeekDayGndUSField;

        private string dropOffWeekDayGndIntlField;

        private string dropOffSatAirDomField;

        private string dropOffSatAirUSField;

        private string dropOffSatAirIntlField;

        private string dropOffSatGndDomField;

        private string dropOffSatGndUSField;

        private string dropOffSatGndIntlField;

        private decimal latitudeField;

        private decimal longitudeField;

        private decimal radialDistanceInKMField;

        /// <remarks/>
        public string LocationNumber
        {
            get
            {
                return this.locationNumberField;
            }
            set
            {
                this.locationNumberField = value;
            }
        }

        /// <remarks/>
        public string LocationType
        {
            get
            {
                return this.locationTypeField;
            }
            set
            {
                this.locationTypeField = value;
            }
        }

        /// <remarks/>
        public string LocationName
        {
            get
            {
                return this.locationNameField;
            }
            set
            {
                this.locationNameField = value;
            }
        }

        /// <remarks/>
        public DepotLocationAddress Address
        {
            get
            {
                return this.addressField;
            }
            set
            {
                this.addressField = value;
            }
        }

        /// <remarks/>
        public string ContactName
        {
            get
            {
                return this.contactNameField;
            }
            set
            {
                this.contactNameField = value;
            }
        }

        /// <remarks/>
        public string PhoneNumber
        {
            get
            {
                return this.phoneNumberField;
            }
            set
            {
                this.phoneNumberField = value;
            }
        }

        /// <remarks/>
        public string SpecialInstructionsEn
        {
            get
            {
                return this.specialInstructionsEnField;
            }
            set
            {
                this.specialInstructionsEnField = value;
            }
        }

        /// <remarks/>
        public string SpecialInstructionsFr
        {
            get
            {
                return this.specialInstructionsFrField;
            }
            set
            {
                this.specialInstructionsFrField = value;
            }
        }

        /// <remarks/>
        public string Depot
        {
            get
            {
                return this.depotField;
            }
            set
            {
                this.depotField = value;
            }
        }

        /// <remarks/>
        public string Unicode
        {
            get
            {
                return this.unicodeField;
            }
            set
            {
                this.unicodeField = value;
            }
        }

        /// <remarks/>
        public bool HoldForPickup
        {
            get
            {
                return this.holdForPickupField;
            }
            set
            {
                this.holdForPickupField = value;
            }
        }

        /// <remarks/>
        public bool DangerousGoods
        {
            get
            {
                return this.dangerousGoodsField;
            }
            set
            {
                this.dangerousGoodsField = value;
            }
        }

        /// <remarks/>
        public bool Kiosk
        {
            get
            {
                return this.kioskField;
            }
            set
            {
                this.kioskField = value;
            }
        }

        /// <remarks/>
        public bool StreetAccess
        {
            get
            {
                return this.streetAccessField;
            }
            set
            {
                this.streetAccessField = value;
            }
        }

        /// <remarks/>
        public bool WheelChairAccess
        {
            get
            {
                return this.wheelChairAccessField;
            }
            set
            {
                this.wheelChairAccessField = value;
            }
        }

        /// <remarks/>
        public string OpenMon
        {
            get
            {
                return this.openMonField;
            }
            set
            {
                this.openMonField = value;
            }
        }

        /// <remarks/>
        public string CloseMon
        {
            get
            {
                return this.closeMonField;
            }
            set
            {
                this.closeMonField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionMon
        {
            get
            {
                return this.openExceptionMonField;
            }
            set
            {
                this.openExceptionMonField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionMon
        {
            get
            {
                return this.closeExceptionMonField;
            }
            set
            {
                this.closeExceptionMonField = value;
            }
        }

        /// <remarks/>
        public string OpenTue
        {
            get
            {
                return this.openTueField;
            }
            set
            {
                this.openTueField = value;
            }
        }

        /// <remarks/>
        public string CloseTue
        {
            get
            {
                return this.closeTueField;
            }
            set
            {
                this.closeTueField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionTue
        {
            get
            {
                return this.openExceptionTueField;
            }
            set
            {
                this.openExceptionTueField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionTue
        {
            get
            {
                return this.closeExceptionTueField;
            }
            set
            {
                this.closeExceptionTueField = value;
            }
        }

        /// <remarks/>
        public string OpenWed
        {
            get
            {
                return this.openWedField;
            }
            set
            {
                this.openWedField = value;
            }
        }

        /// <remarks/>
        public string CloseWed
        {
            get
            {
                return this.closeWedField;
            }
            set
            {
                this.closeWedField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionWed
        {
            get
            {
                return this.openExceptionWedField;
            }
            set
            {
                this.openExceptionWedField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionWed
        {
            get
            {
                return this.closeExceptionWedField;
            }
            set
            {
                this.closeExceptionWedField = value;
            }
        }

        /// <remarks/>
        public string OpenThu
        {
            get
            {
                return this.openThuField;
            }
            set
            {
                this.openThuField = value;
            }
        }

        /// <remarks/>
        public string CloseThu
        {
            get
            {
                return this.closeThuField;
            }
            set
            {
                this.closeThuField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionThu
        {
            get
            {
                return this.openExceptionThuField;
            }
            set
            {
                this.openExceptionThuField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionThu
        {
            get
            {
                return this.closeExceptionThuField;
            }
            set
            {
                this.closeExceptionThuField = value;
            }
        }

        /// <remarks/>
        public string OpenFri
        {
            get
            {
                return this.openFriField;
            }
            set
            {
                this.openFriField = value;
            }
        }

        /// <remarks/>
        public string CloseFri
        {
            get
            {
                return this.closeFriField;
            }
            set
            {
                this.closeFriField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionFri
        {
            get
            {
                return this.openExceptionFriField;
            }
            set
            {
                this.openExceptionFriField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionFri
        {
            get
            {
                return this.closeExceptionFriField;
            }
            set
            {
                this.closeExceptionFriField = value;
            }
        }

        /// <remarks/>
        public string OpenSat
        {
            get
            {
                return this.openSatField;
            }
            set
            {
                this.openSatField = value;
            }
        }

        /// <remarks/>
        public string CloseSat
        {
            get
            {
                return this.closeSatField;
            }
            set
            {
                this.closeSatField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionSat
        {
            get
            {
                return this.openExceptionSatField;
            }
            set
            {
                this.openExceptionSatField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionSat
        {
            get
            {
                return this.closeExceptionSatField;
            }
            set
            {
                this.closeExceptionSatField = value;
            }
        }

        /// <remarks/>
        public string OpenSun
        {
            get
            {
                return this.openSunField;
            }
            set
            {
                this.openSunField = value;
            }
        }

        /// <remarks/>
        public string CloseSun
        {
            get
            {
                return this.closeSunField;
            }
            set
            {
                this.closeSunField = value;
            }
        }

        /// <remarks/>
        public string OpenExceptionSun
        {
            get
            {
                return this.openExceptionSunField;
            }
            set
            {
                this.openExceptionSunField = value;
            }
        }

        /// <remarks/>
        public string CloseExceptionSun
        {
            get
            {
                return this.closeExceptionSunField;
            }
            set
            {
                this.closeExceptionSunField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayAirDom
        {
            get
            {
                return this.dropOffWeekDayAirDomField;
            }
            set
            {
                this.dropOffWeekDayAirDomField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayAirUS
        {
            get
            {
                return this.dropOffWeekDayAirUSField;
            }
            set
            {
                this.dropOffWeekDayAirUSField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayAirIntl
        {
            get
            {
                return this.dropOffWeekDayAirIntlField;
            }
            set
            {
                this.dropOffWeekDayAirIntlField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayGndDom
        {
            get
            {
                return this.dropOffWeekDayGndDomField;
            }
            set
            {
                this.dropOffWeekDayGndDomField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayGndUS
        {
            get
            {
                return this.dropOffWeekDayGndUSField;
            }
            set
            {
                this.dropOffWeekDayGndUSField = value;
            }
        }

        /// <remarks/>
        public string DropOffWeekDayGndIntl
        {
            get
            {
                return this.dropOffWeekDayGndIntlField;
            }
            set
            {
                this.dropOffWeekDayGndIntlField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatAirDom
        {
            get
            {
                return this.dropOffSatAirDomField;
            }
            set
            {
                this.dropOffSatAirDomField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatAirUS
        {
            get
            {
                return this.dropOffSatAirUSField;
            }
            set
            {
                this.dropOffSatAirUSField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatAirIntl
        {
            get
            {
                return this.dropOffSatAirIntlField;
            }
            set
            {
                this.dropOffSatAirIntlField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatGndDom
        {
            get
            {
                return this.dropOffSatGndDomField;
            }
            set
            {
                this.dropOffSatGndDomField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatGndUS
        {
            get
            {
                return this.dropOffSatGndUSField;
            }
            set
            {
                this.dropOffSatGndUSField = value;
            }
        }

        /// <remarks/>
        public string DropOffSatGndIntl
        {
            get
            {
                return this.dropOffSatGndIntlField;
            }
            set
            {
                this.dropOffSatGndIntlField = value;
            }
        }

        /// <remarks/>
        public decimal Latitude
        {
            get
            {
                return this.latitudeField;
            }
            set
            {
                this.latitudeField = value;
            }
        }

        /// <remarks/>
        public decimal Longitude
        {
            get
            {
                return this.longitudeField;
            }
            set
            {
                this.longitudeField = value;
            }
        }

        /// <remarks/>
        public decimal RadialDistanceInKM
        {
            get
            {
                return this.radialDistanceInKMField;
            }
            set
            {
                this.radialDistanceInKMField = value;
            }
        }

        public List<string> BuildAddressDisplay( string languageCode )
        {
            List<string> retVal  = new List<string>();
            if (locationNameField.ValueExists()) retVal.Add(locationNameField);
            if (phoneNumberField.ValueExists()) retVal.Add(phoneNumberField);

            if (SmartSort.Common.Helpers.IsFrench(languageCode))
            {
                if (specialInstructionsFrField.ValueExists()) retVal.Add(specialInstructionsFrField);
            }
            else
            {
                if (specialInstructionsEnField.ValueExists()) retVal.Add(specialInstructionsEnField);
            }
            return retVal;
        }


        public string FormatForHTML( string languageCode )
        {
            StringBuilder db = new StringBuilder();
            List<string> stringArray = BuildAddressDisplay(languageCode);
            foreach (string oneString in stringArray )
            {
                db.AppendFormat("{0}<br/>", oneString);
            }
            return db.ToString();
        }
    }
}
