﻿using System; 
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartSort.ChannelIntegration.Common;
using SmartSort.ChannelIntegration.Entities;
using SmartSort.Common;
namespace SmartSort.ChannelIntegration
{
    public class AddressManager 
    {

        /// <summary>
        /// Gets Suggested Addressed based on Country Code and Postal Code
        /// </summary>
        /// <param name="countryCode">Country Code</param>
        /// <param name="postalCode">Postal Code</param>
        /// <returns>Result<List<SuggestedAddress>></returns>
        private Result<List<SuggestedAddress>> GetAllSuggestedAddresses(string countryCode, string postalCode)
        {
            Result<List<SuggestedAddress>> result = new Result<List<SuggestedAddress>>();
            try
            {
                if (countryCode.ValueEmpty())
                {
                    result.IsSuccess = false;
                }
                else if (postalCode.ValueEmpty())
                {
                    result.IsSuccess = false;
                }
                else
                {
                    string localCountryCode = countryCode.Trim().ToUpper();
                    string localPostalCode = SmartSort.Common.Helpers.StripPostalZipCode(postalCode);

                    if (SmartSort.Common.Helpers.IsCanadaCountryCode(countryCode))
                    {
                       
                    }
                }
            }
            catch (Exception ex)
            {
                    throw ex;
            }
            return result;
        }


        private Result<List<SuggestedAddress>> GetUniqueSuggestedAddresses(string countryCode, string postalCode)
        {
            Result<List<SuggestedAddress>> result = new Result<List<SuggestedAddress>>();
            try
            {
                SortedList<string, SuggestedAddress> sortedAddresses = new SortedList<string, SuggestedAddress>();

                Result<List<SuggestedAddress>> getAllResult = GetAllSuggestedAddresses(countryCode, postalCode);

                if (getAllResult != null && getAllResult.IsSuccess)
                {
                    if (getAllResult.ResultObject != null)
                    {
                        //Loop Through All Suggested Addresses.
                        foreach (SuggestedAddress anAddress in getAllResult.ResultObject)
                        {
                            //Generate Unique Address Key For This Address
                            string lookupKey = (anAddress.StreetName.ValueEmpty() ? "NULL" : anAddress.StreetName) +
                                               (anAddress.StreetType.ValueEmpty() ? "NULL" : anAddress.StreetType) +
                                               (anAddress.StreetDirection.ValueEmpty() ? "NULL" : anAddress.StreetDirection) +
                                               (anAddress.City.ValueEmpty() ? "NULL" : anAddress.City) +
                                               (anAddress.ProvinceCode.ValueEmpty() ? "NULL" : anAddress.ProvinceCode);

                            //Check If Unique Address Key Already Exists in stored Sorted List using KEY
                            if (!sortedAddresses.ContainsKey(lookupKey))
                            {
                                //Does Not Exist...Add Address to List using KEY
                                SuggestedAddress uniqueAddress = (SuggestedAddress)anAddress.Clone();

                                // clear out any fields that could be different
                                uniqueAddress.StreetNumber = null;
                                uniqueAddress.StreetNumberSequenceType = SuggestedAddress.StreetNumberSequence.All;

                                //Add First Address To The List
                                sortedAddresses[lookupKey] = uniqueAddress;
                            }
                            else
                            {
                                //Address Already Exists in List
                                SuggestedAddress uniqueAddress = sortedAddresses[lookupKey];

                                int minStreetNumber = int.MaxValue;
                                int.TryParse(uniqueAddress.FromStreetNumber, out minStreetNumber);
                                int maxStreetNumber = int.MinValue;
                                int.TryParse(uniqueAddress.ToStreetNumber, out maxStreetNumber);
                                int fromStreetNumber = int.MaxValue;
                                int.TryParse(anAddress.FromStreetNumber, out fromStreetNumber);
                                int toStreetNumber = int.MinValue;
                                int.TryParse(anAddress.ToStreetNumber, out toStreetNumber);

                                //Update Street Number Ranges on existing Address if needed.
                                if (fromStreetNumber < minStreetNumber)
                                {
                                    uniqueAddress.FromStreetNumber = anAddress.FromStreetNumber;
                                }
                                if (toStreetNumber > maxStreetNumber)
                                {
                                    uniqueAddress.ToStreetNumber = anAddress.ToStreetNumber;
                                }
                            }
                        }
                    }

                    List<SuggestedAddress> uniqueAddresses = null;
                    if (sortedAddresses != null)
                    {
                        //Copy SortedList to Array[]
                        SuggestedAddress[] tempArray = new SuggestedAddress[sortedAddresses.Values.Count];
                        sortedAddresses.Values.CopyTo(tempArray, 0);
                        //Copy Array to List
                        uniqueAddresses = new List<SuggestedAddress>(tempArray);
                    }

                    //Assign Result
                    result.ResultObject = uniqueAddresses;
                    result.IsSuccess = true;

                }
                else
                {
                    result.IsSuccess = false;
                    result.ResultObject = null;
                    result.AddErrors(getAllResult.GetErrors());
                }
            }
            catch (Exception ex)
            {
              
                    throw ex;
            }
            return result;
        }


        public string[] GetCanadianCitiesByPostalCodeCity(string postalCode, string city, int count)
        {
            return GetCitiesByCountryPostalCodeCity(SmartSort.Common.Constants.COUNTRY_CANADA_CODE, postalCode, city, count);
        }

        private string[] GetCitiesByCountryPostalCodeCity(string countryCode, string postalCode, string city, int count)
        {
            string[] cities = new string[0];
            try
            {
                Result<List<SuggestedAddress>> result = GetAllSuggestedAddresses(countryCode, postalCode);
                if (result != null)
                {
                    if (result.IsSuccess && result.ResultObject != null)
                    {
                        SortedList<string, string> addresses = new SortedList<string, string>();
                        foreach (SuggestedAddress anAddress in result.ResultObject)
                        {
                            if (anAddress.City.ToUpper().StartsWith(city.ToUpper()))
                            {
                                addresses[anAddress.City] = anAddress.City;
                            }
                        }

                        int resultSize = 0;
                        if (addresses.Values.Count > count)
                        {
                            resultSize = count;
                        }
                        else
                        {
                            resultSize = addresses.Values.Count;
                        }

                        cities = new string[resultSize];
                        int index = 0;
                        foreach (string aCity in addresses.Values)
                        {
                            cities[index++] = aCity;
                            if (index > resultSize)
                            {
                                break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
             
                    throw ex;
            }
            return cities;
        }


        public string[] GetCanadianStreetNamesByCountryPostalCodeCityStreetName(string postalCode, string city, string streetName, int count)
        {
            return GetStreetNamesByCountryPostalCodeCityStreetName(SmartSort.Common.Constants.COUNTRY_CANADA_CODE, postalCode, city, streetName, count);
        }


        private string[] GetStreetNamesByCountryPostalCodeCityStreetName(string countryCode, string postalCode, string city, string streetName, int count)
        {
            string[] streetNames = new string[0];
            try
            {
                Result<List<SuggestedAddress>> result = GetAllSuggestedAddresses(countryCode, postalCode);
                if (result != null && result.IsSuccess && result.ResultObject != null)
                {
                    SortedList<string, string> addresses = new SortedList<string, string>();
                    foreach (SuggestedAddress anAddress in result.ResultObject)
                    {
                        if (SmartSort.Common.Helpers.NullToEmptyString(anAddress.City).ToUpper() == city.ToUpper() &&
                            SmartSort.Common.Helpers.NullToEmptyString(anAddress.StreetName).ToUpper().StartsWith(streetName.ToUpper()))
                        {
                            addresses[anAddress.StreetName] = anAddress.StreetName;
                        }
                    }

                    int resultSize = 0;
                    if (addresses.Values.Count > count)
                    {
                        resultSize = count;
                    }
                    else
                    {
                        resultSize = addresses.Values.Count;
                    }

                    streetNames = new string[resultSize];
                    int index = 0;
                    foreach (string aStreetName in addresses.Values)
                    {
                        streetNames[index++] = aStreetName;
                        if (index > resultSize)
                        {
                            break;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
              
                    throw ex;
            }
            return streetNames;
        }


        public SuggestedAddress GetOneUniqueAddress(string postalCode)
        {
            SuggestedAddress correctSuggestedAddress = null;
            Result<List<SuggestedAddress>> result = GetUniqueSuggestedAddresses(SmartSort.Common.Constants.COUNTRY_CANADA_CODE, postalCode);
            if (result != null && result.IsSuccess &&
                result.ResultObject != null && result.ResultObject.Count > 0)
            {
                correctSuggestedAddress = BuildOneUniqueAddress(result.ResultObject).Clone();
            }
            return correctSuggestedAddress;
        }

        private bool StreetNumberCompare(string sourceStreetNumber, SuggestedAddress suggestedAddress)
        {
            bool retVal = true;
            if (sourceStreetNumber.ValueEmpty())
            {
                //Empty Provided
                if (suggestedAddress.FromStreetNumber.ValueExists() || suggestedAddress.ToStreetNumber.ValueExists())
                {
                    //But range is defined.
                    retVal = false;
                }
            }
            else
            {
                //Street Number Provided
                if (suggestedAddress.FromStreetNumber.ValueEmpty() && suggestedAddress.ToStreetNumber.ValueEmpty())
                {
                    //But no range is defined...so anything goes
                    retVal = true;
                }
                else
                {
                    //At least one range limit Defined
                    string sourceStreetNumberString = SmartSort.Common.Helpers.NullToEmptyString(sourceStreetNumber);
                    string compareFromStreetNumberString = SmartSort.Common.Helpers.NullToEmptyString(suggestedAddress.FromStreetNumber);
                    string compareToStreetNumberString = SmartSort.Common.Helpers.NullToEmptyString(suggestedAddress.ToStreetNumber);

                    //In case limit is empty ended...make ends the same.
                    if (compareFromStreetNumberString.ValueEmpty()) compareFromStreetNumberString = compareToStreetNumberString;
                    if (compareToStreetNumberString.ValueEmpty()) compareToStreetNumberString = compareFromStreetNumberString;

                    int sourceStreetNumberInt = 0;
                    int compareFromStreetNumberInt = 0;
                    int compareToStreetNumberInt = 0;

                    if (!int.TryParse(sourceStreetNumberString, out sourceStreetNumberInt) ||
                        !int.TryParse(compareFromStreetNumberString, out compareFromStreetNumberInt) ||
                        !int.TryParse(compareToStreetNumberString, out compareToStreetNumberInt))
                    {
                        //Can't convert something to int
                        retVal = false;
                    }
                    else
                    {
                        if (sourceStreetNumberInt < compareFromStreetNumberInt ||
                            sourceStreetNumberInt > compareToStreetNumberInt)
                        {
                            retVal = false;
                        }
                        else
                        {
                            if (suggestedAddress.StreetNumberSequenceType.Equals(SuggestedAddress.StreetNumberSequence.Even))
                            {
                                //Even Number Test
                                if ((sourceStreetNumberInt % 2) != 0)
                                {
                                    retVal = false;
                                }
                            }
                            else if (suggestedAddress.StreetNumberSequenceType.Equals(SuggestedAddress.StreetNumberSequence.Odd))
                            {
                                //Odd Number Test
                                if ((sourceStreetNumberInt % 2) == 0)
                                {
                                    retVal = false;
                                }
                            }
                            else
                            {
                                //All...no test needed
                            }
                        }
                    }
                }
            }
            return retVal;
        }


        private SuggestedAddress BuildOneUniqueAddress(List<SuggestedAddress> suggestedAddresses)
        {
            SuggestedAddress suggestedAddress = null;

            if (suggestedAddresses != null && suggestedAddresses.Count > 0)
            {
                //More than 1 Suggested Address so we build Suggestion from from Common Suggested Address Elements
                suggestedAddress = new SuggestedAddress();

                SuggestedAddress firstSuggestedAddress = suggestedAddresses[0].Clone();

                //Rollup Suggestions
                bool allSameCountryCode = true;
                bool allSameProvinceCode = true;
                bool allSamePostalCode = true;
                bool allSameCityName = true;
                bool allSameStreetName = true;
                bool allSameStreetType = true;
                bool allSameStreetDirection = true;
                bool allSameFromSuffix = true;
                bool allSameToSuffix = true;
                bool allSameFromStreetNumber = true;
                bool allSameToStreetNumber = true;

                //Check Suggested Addresses for Similarities
                foreach (SuggestedAddress oneSuggestedAddress in suggestedAddresses)
                {
                    //All Matching Country Code?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.CountryCode, firstSuggestedAddress.CountryCode))
                        allSameCountryCode = false;

                    //All Matching Province Code?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.ProvinceCode, firstSuggestedAddress.ProvinceCode))
                        allSameProvinceCode = false;

                    //All Matching Postal Code?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.PostalCode, firstSuggestedAddress.PostalCode))
                        allSamePostalCode = false;

                    //All Matching City Names?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.City, firstSuggestedAddress.City))
                        allSameCityName = false;

                    //All Matching Street Names?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.StreetName, firstSuggestedAddress.StreetName))
                        allSameStreetName = false;

                    //All Matching Street Type?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.StreetType, firstSuggestedAddress.StreetType))
                        allSameStreetType = false;

                    //All Matching Street Direction?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.StreetDirection, firstSuggestedAddress.StreetDirection))
                        allSameStreetDirection = false;

                    //All Matching From Street Suffix?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.FromStreetNumberSuffix, firstSuggestedAddress.FromStreetNumberSuffix))
                        allSameFromSuffix = false;

                    //All Matching To Street Suffix?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.ToStreetNumberSuffix, firstSuggestedAddress.ToStreetNumberSuffix))
                        allSameToSuffix = false;

                    //All Matching From Street Number?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.FromStreetNumber, firstSuggestedAddress.FromStreetNumber))
                        allSameFromStreetNumber = false;

                    //All Matching To Street Number?
                    if (!SmartSort.Common.Helpers.StringCompare(oneSuggestedAddress.ToStreetNumber, firstSuggestedAddress.ToStreetNumber))
                        allSameToStreetNumber = false;
                }

                //Set where same Values
                if (allSameCountryCode) suggestedAddress.CountryCode = firstSuggestedAddress.CountryCode;
                if (allSameProvinceCode) suggestedAddress.ProvinceCode = firstSuggestedAddress.ProvinceCode;
                if (allSamePostalCode) suggestedAddress.PostalCode = firstSuggestedAddress.PostalCode;
                if (allSameCityName) suggestedAddress.City = firstSuggestedAddress.City;
                if (allSameStreetName) suggestedAddress.StreetName = firstSuggestedAddress.StreetName;
                if (allSameStreetType) suggestedAddress.StreetType = firstSuggestedAddress.StreetType;
                if (allSameStreetDirection) suggestedAddress.StreetDirection = firstSuggestedAddress.StreetDirection;

                if (allSameFromSuffix) suggestedAddress.FromStreetNumberSuffix = firstSuggestedAddress.FromStreetNumberSuffix;
                if (allSameToSuffix) suggestedAddress.ToStreetNumberSuffix = firstSuggestedAddress.ToStreetNumberSuffix;
                if (allSameFromSuffix && allSameToSuffix)
                {
                    if (SmartSort.Common.Helpers.StringCompare(suggestedAddress.ToStreetNumberSuffix, suggestedAddress.FromStreetNumberSuffix))
                    {
                        suggestedAddress.StreetSuffix = suggestedAddress.FromStreetNumberSuffix;
                    }
                }

                if (allSameFromStreetNumber) suggestedAddress.FromStreetNumber = firstSuggestedAddress.FromStreetNumber;
                if (allSameToStreetNumber) suggestedAddress.ToStreetNumber = firstSuggestedAddress.ToStreetNumber;
                if (SmartSort.Common.Helpers.StringCompare(suggestedAddress.FromStreetNumber, suggestedAddress.ToStreetNumber, true))
                {
                    suggestedAddress.StreetNumber = suggestedAddress.FromStreetNumber;
                }

                //Set Address Type using "All" Suggested Addresses
                suggestedAddress.AddressType = DetermineSuggestedAddressType(suggestedAddresses.ToArray());
            }

            return suggestedAddress;
        }


        private SuggestedAddress.AddressTypes DetermineSuggestedAddressType(SuggestedAddress[] suggestedAddresses)
        {
            //Determine Address Type using "All" supplied Suggested Addresses
            SuggestedAddress.AddressTypes retVal = SuggestedAddress.AddressTypes.Unknown;
            if (suggestedAddresses != null && suggestedAddresses.Length > 0)
            {
                SuggestedAddress.AddressTypes firstAddressType = suggestedAddresses[0].AddressType;

                bool allSameAddressType = true;
                bool containsRegularType = false;
                bool containsSpecialType = false;

                foreach (SuggestedAddress oneSuggestedAddress in suggestedAddresses)
                {
                    if (!oneSuggestedAddress.AddressType.Equals(firstAddressType))
                    {
                        allSameAddressType = false;
                    }
                    if (oneSuggestedAddress.AddressType.Equals(SuggestedAddress.AddressTypes.Regular))
                    {
                        containsRegularType = true;
                    }
                    else if (oneSuggestedAddress.AddressType.Equals(SuggestedAddress.AddressTypes.GeneralDelivery))
                    {
                        containsSpecialType = true;
                    }
                    else if (oneSuggestedAddress.AddressType.Equals(SuggestedAddress.AddressTypes.PostOfficeBox))
                    {
                        containsSpecialType = true;
                    }
                    else if (oneSuggestedAddress.AddressType.Equals(SuggestedAddress.AddressTypes.RuralRoute))
                    {
                        containsSpecialType = true;
                    }
                    else
                    {
                        containsRegularType = true;
                    }
                }

                //Set Address Type
                if (allSameAddressType)
                {
                    //All The Same Type
                    retVal = firstAddressType;
                }
                else
                {
                    //Not All The Same Address Type (Use Extended Types if needed)
                    if (containsRegularType && containsSpecialType)
                    {
                        retVal = SuggestedAddress.AddressTypes.Mixed;
                    }
                    else if (containsRegularType && !containsSpecialType)
                    {
                        retVal = SuggestedAddress.AddressTypes.Regular;
                    }
                    else if (!containsRegularType && containsSpecialType)
                    {
                        retVal = SuggestedAddress.AddressTypes.Special;
                    }
                    else if (!containsRegularType && !containsSpecialType)
                    {
                        retVal = SuggestedAddress.AddressTypes.Regular;
                    }
                }
            }
            return retVal;
        }


        private static string MakeCountryPostalCodeKey(string countryCode, string postalCode)
        {
            string cacheKey = String.Empty;

            if (countryCode.ValueExists())
                cacheKey += countryCode.ToUpper().Trim();
            else
                cacheKey += "NULL";

            cacheKey += "_";

            if (postalCode.ValueExists())
                cacheKey += postalCode.ToUpper().Trim();
            else
                cacheKey += "NULL";

            return cacheKey;
        }

    }
}
