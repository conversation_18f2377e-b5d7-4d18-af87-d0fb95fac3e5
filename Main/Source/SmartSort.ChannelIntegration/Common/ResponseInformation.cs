﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using SmartSort.Common;

namespace SmartSort.ChannelIntegration.Common
{
    public class ResponseInformation
    {
        public bool IsSuccess
        {
            get
            {
                if (GetErrors().Count == 0)
                    return true;
                else
                    return false;
            }
        }

        private string _ResponseReference;
        public string ResponseReference
 
        {
            get
            {
                return _ResponseReference;
            }
            set
            {
                _ResponseReference = value;
            }
        }

        public void AddError(Error error)
        {
            if (error != null)
            {
                if (_errors == null)
                {
                    _errors = new List<Error>();
                }
                _errors.Add(error);
            }
        }

        public void AddErrors(List<Error> errors)
        {
            if (errors != null)
            {
                if (_errors == null)
                {
                    _errors = new List<Error>();
                }
                _errors.AddRange(errors);
            }
        }

        private List<Error> _errors;
        public List<Error> GetErrors()
        {
            if (_errors == null)
            {
                _errors = new List<Error>();
            }
            return _errors;
        }

        public bool HasErrors()
        {
            return GetErrors() != null && GetErrors().Count > 0;
        }

        private List<InformationalMessage> _informationalMessages;
        public List<InformationalMessage> InformationalMessages 
        {
            get
            {
                return _informationalMessages;
            }
            set
            {
                _informationalMessages = value;
            }
        }
    }
}
