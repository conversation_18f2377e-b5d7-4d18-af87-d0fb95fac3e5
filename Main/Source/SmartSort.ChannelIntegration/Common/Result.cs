﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using SmartSort.Common;


namespace SmartSort.ChannelIntegration.Common
{
    public class Result<TDomainObject>
    {
        /// <summary>
        /// The generic type of the return object.
        /// </summary>
        private TDomainObject _resultObject;
        /// <summary>
        /// The list of the errors.
        /// </summary>
        private ResponseInformation _response;

        /// <summary>
        /// Initializes a new instance of the BOResult class.
        /// </summary>
        public Result()
        {
            _resultObject = default(TDomainObject);
            _response = new ResponseInformation();
        }

        /// <summary>
        /// Add Error to Response Information.
        /// </summary>
        public void AddError(string code, string description, string additionalInfo)
        {
            Error newError = new Error(code, description, additionalInfo);
            if (_response == null)
            {
                _response = new ResponseInformation();
            }
            _response.AddError(newError);
        }

        /// <summary>
        /// Add Error to Response Information.
        /// </summary>
        public void AddError(Error error)
        {
            if (_response == null)
            {
                _response = new ResponseInformation();
            }
            _response.AddError(error);
        }

        /// <summary>
        /// Add a list of errors to the error list.
        /// </summary>
        /// <param name="error">The error object.</param>
        public void AddErrors(List<Error> errList)
        {
            if (errList != null)
            {
                if (_response == null)
                {
                    _response = new ResponseInformation();
                }
                _response.AddErrors(errList);
            }
        }


        /// <summary>
        /// Indicates if the call succeeds. The default value is false.
        /// </summary>
        bool _isSuccess = false;
        public bool IsSuccess
        {
            get
            {
                if (!_isSuccess || (_response != null && !_response.IsSuccess) )
                    return false;
                else
                    return true;
                     
            }
            set
            {
                _isSuccess = value;
            }
        }

        /// <summary>
        /// The return object returned from the call. It is a generic type.
        /// </summary>
        public TDomainObject ResultObject
        {
            get { return _resultObject; }
            set { _resultObject = value; }
        }

        /// <summary>
        /// Indicates if there is any error in the return.
        /// </summary>
        /// <returns>if there is error, return ture; otherwise, return false.</returns>
        public bool HasError()
        {
            return (GetErrors().Count > 0);
        }

        /// <summary>
        /// Get the error list.
        /// </summary>
        /// <returns></returns>
        public List<Error> GetErrors()
        {
            if (_response == null)
            {
                _response = new ResponseInformation();
            }
            return _response.GetErrors();
        }

    }
}
