﻿using System.Drawing;
using System.Xml;
using System.Xml.Linq;

namespace OpenXmlPackaging {

    /// <summary>
    /// Represents a <font> element in Stylesheet
    /// </summary>
    public class Font : XElementWriter {       

        #region Public Properties

        public string Name { get; set; }

        public decimal Size { get; set; }

        public FontStyles Style { get; set; }

        public Color Color { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="Font"/> class with
        /// Name  = Calibri,
        /// Size  = 11,
        /// Style = FontStyles.Regular,
        /// Color = Color.Black
        /// </summary>
        public Font() {
            Name = "Calibri";
            Size = 11;
            Style = FontStyles.Regular;
            Color = Color.Black;
        }

        #endregion

        #region XElementWriter Members

        internal override string ParentNode {
            get { return "fonts"; }
        }

        internal override XElement Element {
            get {

                var element = new XElement(Constants.MainXNamespace + "font",
                            new XElement(Constants.MainXNamespace + "name", new XAttribute("val", Name)),
                            new XElement(Constants.MainXNamespace + "sz", new XAttribute("val", Size)),
                            new XElement(Constants.MainXNamespace + "color", new XAttribute("rgb", Utilities.GetColorInHex(Color))));

                AddFontStyles(element);

                return element;
            }
        }

        #endregion

        #region Private Methods

        private void AddFontStyles(XElement element) {
            if (Style != FontStyles.Regular) {
                if (Style.HasFlag(FontStyles.Bold)) {
                    element.Add(new XElement(Constants.MainXNamespace + "b"));
                }
                if (Style.HasFlag(FontStyles.Italic)) {
                    element.Add(new XElement(Constants.MainXNamespace + "i"));
                }
                if (Style.HasFlag(FontStyles.Strikeout)) {
                    element.Add(new XElement(Constants.MainXNamespace + "strike"));
                }
                if (Style.HasFlag(FontStyles.Underline)) {
                    element.Add(new XElement(Constants.MainXNamespace + "u"));
                }
                if (Style.HasFlag(FontStyles.DoubleUnderline)) {
                    element.Add(new XElement(Constants.MainXNamespace + "u", new XAttribute("val", "double")));
                }
                if (Style.HasFlag(FontStyles.Superscript)) {
                    element.Add(new XElement(Constants.MainXNamespace + "vertAlign", new XAttribute("val", "superscript")));
                }
                if (Style.HasFlag(FontStyles.Subscript)) {
                    element.Add(new XElement(Constants.MainXNamespace + "vertAlign", new XAttribute("val", "subscript")));
                }
            }
        }

        private void AddFontStyles(XmlWriter writer) {
            if (Style != FontStyles.Regular) {
                if (Style.HasFlag(FontStyles.Bold)) {
                    writer.WriteElementString("b", Constants.MainNamespace);
                }
                if (Style.HasFlag(FontStyles.Italic)) {
                    writer.WriteElementString("i", Constants.MainNamespace);
                }
                if (Style.HasFlag(FontStyles.Strikeout)) {
                    writer.WriteElementString("strike", Constants.MainNamespace);
                }
                if (Style.HasFlag(FontStyles.Underline)) {
                    writer.WriteElementString("u", Constants.MainNamespace);
                }
                if (Style.HasFlag(FontStyles.DoubleUnderline)) {
                    writer.WriteStartElement("u", Constants.MainNamespace);
                    writer.WriteAttributeString("val", "double");
                    writer.WriteEndElement();
                }
                if (Style.HasFlag(FontStyles.Superscript)) {
                    writer.WriteStartElement("vertAlign", Constants.MainNamespace);
                    writer.WriteAttributeString("val", "superscript");
                    writer.WriteEndElement();
                }
                if (Style.HasFlag(FontStyles.Subscript)) {
                    writer.WriteStartElement("vertAlign", Constants.MainNamespace);
                    writer.WriteAttributeString("val", "subscript");
                    writer.WriteEndElement();
                }
            }
        }

        #endregion

        //internal override bool Write(System.Xml.XmlWriter writer) {

        //    writer.WriteStartElement("font", Constants.MainNamespace);

        //    writer.WriteStartElement("name", Constants.MainNamespace);
        //    writer.WriteAttributeString("val", Name);
        //    writer.WriteEndElement();

        //    writer.WriteStartElement("u", Constants.MainNamespace);
        //    writer.WriteAttributeString("val", Size.ToString());
        //    writer.WriteEndElement();

        //    writer.WriteStartElement("color", Constants.MainNamespace);
        //    writer.WriteAttributeString("rgb", Utilities.GetColorInHex(Color));
        //    writer.WriteEndElement();

        //    AddFontStyles(writer);

        //    writer.WriteEndElement();

        //    return false;
        //}
    }
}
