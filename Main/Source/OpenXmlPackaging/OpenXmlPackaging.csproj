﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DAD3387A-470A-4484-9427-A8C8FAEC6C0F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>OpenXmlPackaging</RootNamespace>
    <AssemblyName>OpenXmlPackaging</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Alignment.cs" />
    <Compile Include="Attributes.cs" />
    <Compile Include="AutoFilters.cs" />
    <Compile Include="Border.cs" />
    <Compile Include="Borders.cs" />
    <Compile Include="Cell.cs" />
    <Compile Include="Column.cs" />
    <Compile Include="Columns.cs" />
    <Compile Include="DataWriter.cs" />
    <Compile Include="MergeCell.cs" />
    <Compile Include="MergedCells.cs" />
    <Compile Include="Range.cs" />
    <Compile Include="Style.cs" />
    <Compile Include="Cells.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="Enums.cs" />
    <Compile Include="Extensions.cs" />
    <Compile Include="Fill.cs" />
    <Compile Include="Font.cs" />
    <Compile Include="Writer.cs" />
    <Compile Include="XElementWriter.cs" />
    <Compile Include="NumberFormat.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Row.cs" />
    <Compile Include="SpreadsheetDocument.cs" />
    <Compile Include="Stylesheet.cs" />
    <Compile Include="Utilities.cs" />
    <Compile Include="Workbook.cs" />
    <Compile Include="Worksheet.cs" />
    <Compile Include="Worksheets.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>