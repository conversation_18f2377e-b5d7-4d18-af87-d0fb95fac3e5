<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="GetSAPUserName">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="token" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetSAPUserNameResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetSAPUserNameResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>