<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="1f823dca-db46-49ac-ba93-371ce9605796" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>true</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings>
      <CollectionMapping TypeName="System.Collections.Generic.List`1" Category="List" />
    </CollectionMappings>
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://localhost:8080/SSO.svc?wsdl" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="SSO.xsd" MetadataType="Schema" ID="49347ef5-58cb-4c41-b669-4da5577ba01f" SourceId="1" SourceUrl="http://localhost:8080/SSO.svc?xsd=xsd0" />
    <MetadataFile FileName="SSO1.xsd" MetadataType="Schema" ID="ed605b73-5dd4-4036-b978-872443b099a1" SourceId="1" SourceUrl="http://localhost:8080/SSO.svc?xsd=xsd1" />
    <MetadataFile FileName="SSOService.wsdl" MetadataType="Wsdl" ID="146d84f6-52da-45b8-8817-c15ab3a072da" SourceId="1" SourceUrl="http://localhost:8080/SSO.svc?wsdl" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>