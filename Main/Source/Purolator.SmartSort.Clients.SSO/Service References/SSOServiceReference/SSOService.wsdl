<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="SSOService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:8080/SSO.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:8080/SSO.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="ISSOService_GetSAPUserName_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSAPUserName" />
  </wsdl:message>
  <wsdl:message name="ISSOService_GetSAPUserName_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSAPUserNameResponse" />
  </wsdl:message>
  <wsdl:portType name="ISSOService">
    <wsdl:operation name="GetSAPUserName">
      <wsdl:input wsaw:Action="http://tempuri.org/ISSOService/GetSAPUserName" message="tns:ISSOService_GetSAPUserName_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ISSOService/GetSAPUserNameResponse" message="tns:ISSOService_GetSAPUserName_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_ISSOService" type="tns:ISSOService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetSAPUserName">
      <soap:operation soapAction="http://tempuri.org/ISSOService/GetSAPUserName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="SSOService">
    <wsdl:port name="BasicHttpBinding_ISSOService" binding="tns:BasicHttpBinding_ISSOService">
      <soap:address location="http://localhost:8080/SSO.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>