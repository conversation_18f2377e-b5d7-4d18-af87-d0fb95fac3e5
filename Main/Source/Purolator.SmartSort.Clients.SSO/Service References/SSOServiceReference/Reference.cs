﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.Clients.SSO.SSOServiceReference {
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="SSOServiceReference.ISSOService")]
    public interface ISSOService {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISSOService/GetSAPUserName", ReplyAction="http://tempuri.org/ISSOService/GetSAPUserNameResponse")]
        Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameResponse GetSAPUserName(Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/ISSOService/GetSAPUserName", ReplyAction="http://tempuri.org/ISSOService/GetSAPUserNameResponse")]
        System.Threading.Tasks.Task<Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameResponse> GetSAPUserNameAsync(Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSAPUserName", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetSAPUserNameRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string token;
        
        public GetSAPUserNameRequest() {
        }
        
        public GetSAPUserNameRequest(string token) {
            this.token = token;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetSAPUserNameResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetSAPUserNameResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string GetSAPUserNameResult;
        
        public GetSAPUserNameResponse() {
        }
        
        public GetSAPUserNameResponse(string GetSAPUserNameResult) {
            this.GetSAPUserNameResult = GetSAPUserNameResult;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ISSOServiceChannel : Purolator.SmartSort.Clients.SSO.SSOServiceReference.ISSOService, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class SSOServiceClient : System.ServiceModel.ClientBase<Purolator.SmartSort.Clients.SSO.SSOServiceReference.ISSOService>, Purolator.SmartSort.Clients.SSO.SSOServiceReference.ISSOService {
        
        public SSOServiceClient() {
        }
        
        public SSOServiceClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public SSOServiceClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SSOServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public SSOServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameResponse GetSAPUserName(Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameRequest request) {
            return base.Channel.GetSAPUserName(request);
        }
        
        public System.Threading.Tasks.Task<Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameResponse> GetSAPUserNameAsync(Purolator.SmartSort.Clients.SSO.SSOServiceReference.GetSAPUserNameRequest request) {
            return base.Channel.GetSAPUserNameAsync(request);
        }
    }
}
