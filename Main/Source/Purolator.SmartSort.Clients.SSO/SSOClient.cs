﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartSort.Common;
using System.Diagnostics;
using Microsoft.Practices.EnterpriseLibrary.Logging;

namespace Purolator.SmartSort.Clients.SSO
{
    public class SSOClient
    {
        public string GetSAPUsername(string token)
        {
            string result = null;
            try
            {
                SSOServiceReference.SSOServiceClient client = new SSOServiceReference.SSOServiceClient();                
                SSOServiceReference.GetSAPUserNameRequest request = new SSOServiceReference.GetSAPUserNameRequest();
                request.token = token;
                var wsResult = client.GetSAPUserName(request);
                result = wsResult.GetSAPUserNameResult;
            }
            catch (Exception ex)
            {                
                SmartSortLogger.Error("Error calling SSO web service", ex);
            }
            return result;
        }
    }
}
