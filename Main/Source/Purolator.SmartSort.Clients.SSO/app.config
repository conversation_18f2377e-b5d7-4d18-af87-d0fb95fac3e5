﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="BasicHttpBinding_ISSOService" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://localhost:8080/SSO.svc" binding="basicHttpBinding"
                bindingConfiguration="BasicHttpBinding_ISSOService" contract="SSOServiceReference.ISSOService"
                name="BasicHttpBinding_ISSOService" />
        </client>
    </system.serviceModel>
</configuration>