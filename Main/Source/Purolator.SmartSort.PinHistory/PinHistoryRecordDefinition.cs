﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Purolator.SmartSort.PinHistory
{
    class PinHistoryRecordDefinition
    {
        public bool IsList { get; set; }
        public string Display { get; set; }
        public Type  EntityType { get; set; }
        public string EntityFieldName { get; set; }
        public PinHistoryRecordDefinition[] ArrayDefinition { get; set; }

        public PinHistoryRecordDefinition(string display, Type entityType, string fieldName, bool isList = false, PinHistoryRecordDefinition[] arrayDef = null)
        {            
            Display = display;
            EntityType = entityType;
            EntityFieldName = fieldName;
            IsList = isList;
            ArrayDefinition = arrayDef;
        }
    }
}
