﻿using System;
using System.IO;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using OpenXmlPackaging;
using System.Drawing;
using SmartSort.Business.Entities.PinHistory;
using Purolator.SmartSort.PinHistory.Resources;

namespace Purolator.SmartSort.PinHistory
{
    public class PinHistoryGenerator
    {

        private static PinHistoryRecordDefinition[] ShipmentManifestInfoText =
        {            
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMCreateDateTime , typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.CreateDateTime)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMDeclaredAddress, typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.DeclaredAddress)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMCurrentAddress, typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.CurrentAddress)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMPurolatorAVStatus, typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.PurolatorAVStatus)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMPurolatorAVInfo, typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.PurolatorAVInfo)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSMCurrentAddressSource, typeof(PinHistoryShipmentManifest), MemberHelper.GetFieldName((PinHistoryShipmentManifest x) => x.CurrentAddressSource))
        };

        private static PinHistoryRecordDefinition[] AddressTriageInfoText = 
        {
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATTriageType, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.TriageType)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATLastUpdateDateTime, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.LastUpdateDateTime)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATLastModifyBy, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.LastModifyBy)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATEscalation, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.UNUSED)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATEscalatedBy, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.UNUSED)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATEscalatedDT, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.UNUSED)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATCorrectedAddress, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.CorrectedAddress)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATRoute, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.Route)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATShelf, typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.Shelf)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATTriageReasonCode,  typeof(PinHistoryAddressTriageInfo), MemberHelper.GetFieldName((PinHistoryAddressTriageInfo x) => x.TriageReasonCode))
        };


        private static PinHistoryRecordDefinition[] AddressTriageQueueText = 
        {
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATLastUpdateDateTime, typeof(PinHistoryAddressTriageQueue), MemberHelper.GetFieldName((PinHistoryAddressTriageQueue x) => x.LastUpdateDateTime)),             
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATLastModifyBy, typeof(PinHistoryAddressTriageQueue), MemberHelper.GetFieldName((PinHistoryAddressTriageQueue x) => x.LastModifyBy)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATCorrectedAddress, typeof(PinHistoryAddressTriageQueue), MemberHelper.GetFieldName((PinHistoryAddressTriageQueue x) => x.CorrectedAddress)),             
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txATTriageReasonCode, typeof(PinHistoryAddressTriageQueue), MemberHelper.GetFieldName((PinHistoryAddressTriageQueue x) => x.TriageReasonCode))
        };

        private static PinHistoryRecordDefinition[] SSReasons =  
        {
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSSScanTime, typeof(PinHistorySSReason), MemberHelper.GetFieldName((PinHistorySSReason x) => x.ScanTime)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSSReason, typeof(PinHistorySSReason), MemberHelper.GetFieldName((PinHistorySSReason x) => x.Reason)),
        };

        private static PinHistoryRecordDefinition[] SmartSortInfoText =
        { 
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSITerminal, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.Terminal)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIRoutePlanVersionID, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.RoutePlanVersionID)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIParkingPlanCodeID, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.ParkingPlanCodeID)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIPrimarySort, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.PrimarySort)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIBeltSide, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.BeltSide)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIRoute, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.Route)),                
            new PinHistoryRecordDefinition(string.Empty, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.SSStatusReasons), true, SSReasons),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSICorrectedAddress, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.CorrectedAddress)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIShelf, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.Shelf)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIDeliverySeqID, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.DeliverySeqID)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSITruckShelfOverride, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.TruckShelfOverride)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSISmartSortMode, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.SmartSortMode)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIBarcodeType, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.BarcodeType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIDeliveryTime, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.DeliveryTime)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIDiversionCode, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.DiversionCode)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIPackageType, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.PackageType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIPreprintID, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.PreprintID)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIShipmentType, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.ShipmentType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIHandlingClassType, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.HandlingClassType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIDeliveryType, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.DeliveryType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIResolvedBy, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.ResolvedBy)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txSIAlternateAddressFlag, typeof(PinHistorySmartSortInfo), MemberHelper.GetFieldName((PinHistorySmartSortInfo x) => x.AlternateAddressFlag)),
        };


        private static PinHistoryRecordDefinition[] PDTAction =  
        {
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTAScanDateTimeTZ, typeof(PinHistoryPDTAction), MemberHelper.GetFieldName((PinHistoryPDTAction x) => x.ScanDateTimeTZ)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTAName, typeof(PinHistoryPDTAction), MemberHelper.GetFieldName((PinHistoryPDTAction x) => x.Name)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTAPDTDeviceID, typeof(PinHistoryPDTAction), MemberHelper.GetFieldName((PinHistoryPDTAction x) => x.PDTDeviceID)),
        };

        private static PinHistoryRecordDefinition[] PDTInfoText = 
        {
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTPDTDeviceID, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.PDTDeviceID)),
             new PinHistoryRecordDefinition(string.Empty, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.Actions), true, PDTAction),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTRouteID, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.RouteID)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTRoute, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.Route)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTShelf, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.Shelf)),
             new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPDTTruckShelfOverride, typeof(PinHistoryPDTInfo), MemberHelper.GetFieldName((PinHistoryPDTInfo x) => x.TruckShelfOverride))
        };


        private static PinHistoryRecordDefinition[] CourierManifestInfoText =
        {
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMCMDownloadTime, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.CMDownloadTime)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMCMDownloadSource, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.CMDownloadSource)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMRoute, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.Route)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMCustomerName, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.CustomerName)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMUnitNumber, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.UnitNumber)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMStreetNumber, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.StreetNumber)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMStreetNumberSuf, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.StreetNumberSuf)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMStreetName, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.StreetName)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMStreetType, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.StreetType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMStreetDirection, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.StreetDirection)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMCity, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.City)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMPostalCode, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.PostalCode)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMShelf, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.Shelf)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMDeliverySeqId, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.DeliverySeqId)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMTruckShelfOverride, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.TruckShelfOverride)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMPackageType, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.PackageType)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMCustomerStopID, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.CustomerStopID)),
            new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txCMDriverStopId, typeof(PinHistoryCourierManifest), MemberHelper.GetFieldName((PinHistoryCourierManifest x) => x.DriverStopId))            
        };
       
        private static PinHistoryRecordDefinition[] PinMasterCode =  
        {
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPMStateCode, typeof(PinMasterStateCode), MemberHelper.GetFieldName((PinMasterStateCode x) => x.StateCode)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPMLastUpdateDateTime, typeof(PinMasterStateCode), MemberHelper.GetFieldName((PinMasterStateCode x) => x.LastUpdateDateTime)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPMLastModifyBy, typeof(PinMasterStateCode), MemberHelper.GetFieldName((PinMasterStateCode x) => x.LastModifyBy)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPMComment, typeof(PinMasterStateCode), MemberHelper.GetFieldName((PinMasterStateCode x) => x.Comment)),
          new PinHistoryRecordDefinition(PinHistoryGeneratorResource.txPMCreatedDateTime, typeof(PinMasterStateCode), MemberHelper.GetFieldName((PinMasterStateCode x) => x.CreatedDateTime)),
        };

        private static PinHistoryRecordDefinition[] PinMasterStateCodeLogText = 
        {             
             new PinHistoryRecordDefinition(string.Empty, typeof(PinHistoryPinMasterLog), MemberHelper.GetFieldName((PinHistoryPinMasterLog x) => x.pinMasterCodes), true, PinMasterCode),             
        };


        private void SetContentStyle(Worksheet sheet1, string celNumber, Color color, bool bold = false, BorderStyles border = BorderStyles.Thin)
        {
            sheet1.Cells[celNumber].Style = new Style
            {
                Borders = new Borders(border),
                Font = new OpenXmlPackaging.Font
                {
                    Name = "Consolas",
                    Size = 10,
                    Color = Color.Black,
                    Style = bold? FontStyles.Bold : FontStyles.Regular
                },
                NumberFormat = new NumberFormat("#"),
                Alignment = new Alignment
                {
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Middle,
                    WrapText = true
                },
                Fill = new Fill { Color = color }
            };
        }

        private void SetHeaderStyle(Worksheet sheet1, string celNumber)
        {
            SetContentStyle(sheet1, celNumber, Color.MediumAquamarine, true, BorderStyles.Medium);            
        }

        public void GeneratePinHistory(Stream output, PinHistoryList pinHistoryList)
        {
            SpreadsheetDocument doc = null;
            
                using (doc = new SpreadsheetDocument(output, FileMode.CreateNew))
                {
                    Worksheet sheet1 = doc.Worksheets.Add(PinHistoryGeneratorResource.txWorksheetTitle);

                    List<PinHistoryDetails> pins = pinHistoryList.Pins;
                    
                    // headers
                    SetHeaderStyle(sheet1, "A1");
                    SetHeaderStyle(sheet1, "B1");
                    sheet1.Cells["B1"].Value = PinHistoryGeneratorResource.txPins;

                    char startCell = 'C';
                    for (int i = 0; i < pins.Count; i++)
                    {
                        string celNumber = (char)(startCell + i) + "1";
                        SetHeaderStyle(sheet1, celNumber);
                        sheet1.Cells[celNumber].Value = pins[i].Pin;
                    }

                    int rowIndex = 2;

                    // content
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdSMinfo, ShipmentManifestInfoText, pins, Color.LightBlue);
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdATInfo, AddressTriageInfoText, pins, Color.Yellow);
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdATQInfo, AddressTriageQueueText, pins, Color.Beige);                    
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdSSInfo, SmartSortInfoText, pins, Color.LightSeaGreen);
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdPDTInfo, PDTInfoText, pins, Color.LightGray);
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdCMInfo, CourierManifestInfoText, pins, Color.LightGoldenrodYellow);
                    rowIndex += CreateSection(sheet1, rowIndex, PinHistoryGeneratorResource.hdPMInfo, PinMasterStateCodeLogText, pins, Color.AliceBlue);




                    sheet1.AutoFitColumns();
                }            
            
        }


        private int CreateSection(Worksheet sheet, int rowIndex, string header, PinHistoryRecordDefinition[] recordDefinition, List<PinHistoryDetails> pins, Color color)
        {
            int totalRows = 0;
            for (int i = 0; i < recordDefinition.Length; i++)
            {

                if (recordDefinition[i].IsList)
                {
                    int maxArrayRows = 0;
                    for (int j = 0; j < pins.Count; j++)
                    {
                        object obj = pins[j].GetValue(recordDefinition[i].EntityType, recordDefinition[i].EntityFieldName);
                        if(obj != null && obj.GetType() != typeof(string))
                        {
                            object[] array = (object[])obj;
                            if (maxArrayRows < array.Length)
                            {
                                maxArrayRows = array.Length;
                            }
                        }
                    }

                    totalRows += maxArrayRows * recordDefinition[i].ArrayDefinition.Length;
                }
                else
                {
                    totalRows++;
                }
            }
            sheet.Cells.CreateRange("A" + rowIndex + ":A" + (rowIndex + totalRows - 1)).MergeCells();

            SetContentStyle(sheet, "A" + rowIndex, color);
            sheet.Cells["A" + rowIndex].Value = header;
            
            int rowCount = 0;
            for (int i = 0; i < recordDefinition.Length; i++)
            {
                if (recordDefinition[i].IsList)
                {
                    int maxArrayRows = 0;
                    
                    for (int j = 0; j < pins.Count; j++)
                    {
                        object obj = pins[j].GetValue(recordDefinition[i].EntityType, recordDefinition[i].EntityFieldName);
                        if (obj != null && obj.GetType() != typeof(string))
                        {
                            object[] array = (object[])obj;
                            if (maxArrayRows < array.Length)
                            {
                                maxArrayRows = array.Length;
                            }
                        }
                    }
                    
                    PinHistoryRecordDefinition[] arrayDef = recordDefinition[i].ArrayDefinition;
                    
                    for (int j = 0; j < maxArrayRows; j++)
                    {
                        for (int k = 0; k < arrayDef.Length; k++)
                        {                           
                            string celNumber = 'B'.ToString() + (rowCount + rowIndex);
                            SetContentStyle(sheet, celNumber, color);
                            sheet.Cells[celNumber].Value = string.Format(arrayDef[k].Display, j + 1);

                            for (int l = 0; l < pins.Count; l++)
                            {
                                string dataCelNumber = ((char)('C' + l)).ToString() + (rowCount + rowIndex);
                                SetContentStyle(sheet, dataCelNumber, color);

                                object obj = pins[l].GetValue(recordDefinition[i].EntityType, recordDefinition[i].EntityFieldName);
                                if (obj != null && obj.GetType() != typeof(string))
                                {
                                    object[] arrValue = (object[])obj;

                                    if (j < arrValue.Length)
                                    {
                                        sheet.Cells[dataCelNumber].Value = ((PinHistoryEntity)arrValue[j]).GetFieldValue(arrayDef[k].EntityFieldName);
                                    }
                                }
                            }
                            rowCount++;
                        }
                    }

                }
                else
                {                   
                    string celNumber = 'B'.ToString() + (rowCount + rowIndex);
                    SetContentStyle(sheet, celNumber, color);
                    sheet.Cells[celNumber].Value = recordDefinition[i].Display;
                               
                    for (int j = 0; j < pins.Count; j++)
                    {
                        string dataCelNumber = ((char)('C' + j)).ToString() + (rowIndex + rowCount);
                        SetContentStyle(sheet, dataCelNumber, color);                    
                        string value = (string)pins[j].GetValue(recordDefinition[i].EntityType, recordDefinition[i].EntityFieldName);
                        sheet.Cells[dataCelNumber].Value = value;
                    }
                    rowCount++;
                }
            }
            return rowCount;
        }

    }
}
