﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="hdATInfo" xml:space="preserve">
    <value>Address Triage Info</value>
  </data>
  <data name="hdCMInfo" xml:space="preserve">
    <value>Courier Manifest Info</value>
  </data>
  <data name="hdPDTInfo" xml:space="preserve">
    <value>PDT Info</value>
  </data>
  <data name="hdSMinfo" xml:space="preserve">
    <value>Shipment manifest info</value>
  </data>
  <data name="hdSSInfo" xml:space="preserve">
    <value>Smart Sort info</value>
  </data>
  <data name="txATCorrectedAddress" xml:space="preserve">
    <value>Corrected Address</value>
  </data>
  <data name="txATEscalatedBy" xml:space="preserve">
    <value>Triage escalated by</value>
  </data>
  <data name="txATEscalatedDT" xml:space="preserve">
    <value>Triage escalation date/time</value>
  </data>
  <data name="txATEscalation" xml:space="preserve">
    <value>Triage escalation</value>
  </data>
  <data name="txATLastModifyBy" xml:space="preserve">
    <value>Triaged by</value>
  </data>
  <data name="txATLastUpdateDateTime" xml:space="preserve">
    <value>Triage date</value>
  </data>
  <data name="txATRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txATShelf" xml:space="preserve">
    <value>Shelf</value>
  </data>
  <data name="txATTriageReasonCode" xml:space="preserve">
    <value>Triage Reason code</value>
  </data>
  <data name="txATTriageType" xml:space="preserve">
    <value>Address triage State Code</value>
  </data>
  <data name="txCMCity" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="txCMCMDownloadSource" xml:space="preserve">
    <value>Courier manifest download source</value>
  </data>
  <data name="txCMCMDownloadTime" xml:space="preserve">
    <value>Courier manifest download time</value>
  </data>
  <data name="txCMCustomerName" xml:space="preserve">
    <value>CustomerName</value>
  </data>
  <data name="txCMCustomerStopID" xml:space="preserve">
    <value>CustomerStopID</value>
  </data>
  <data name="txCMDeliverySeqId" xml:space="preserve">
    <value>DeliverySeqId</value>
  </data>
  <data name="txCMDriverStopId" xml:space="preserve">
    <value>DriverStopId</value>
  </data>
  <data name="txCMPackageType" xml:space="preserve">
    <value>PackageType</value>
  </data>
  <data name="txCMPostalCode" xml:space="preserve">
    <value>PostalCode</value>
  </data>
  <data name="txCMRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txCMShelf" xml:space="preserve">
    <value>Shelf</value>
  </data>
  <data name="txCMStreetDirection" xml:space="preserve">
    <value>StreetDirection</value>
  </data>
  <data name="txCMStreetName" xml:space="preserve">
    <value>StreetName</value>
  </data>
  <data name="txCMStreetNumber" xml:space="preserve">
    <value>StreetNumber</value>
  </data>
  <data name="txCMStreetNumberSuf" xml:space="preserve">
    <value>StreetNumberSuf</value>
  </data>
  <data name="txCMStreetType" xml:space="preserve">
    <value>StreetType</value>
  </data>
  <data name="txCMTruckShelfOverride" xml:space="preserve">
    <value>TruckShelfOverride</value>
  </data>
  <data name="txCMUnitNumber" xml:space="preserve">
    <value>UnitNumber</value>
  </data>
  <data name="txPDTAName" xml:space="preserve">
    <value>Action {0}</value>
  </data>
  <data name="txPDTAPDTDeviceID" xml:space="preserve">
    <value>PDTDeviceID {0}</value>
  </data>
  <data name="txPDTAScanDateTimeTZ" xml:space="preserve">
    <value>Action {0} ScanDateTimeTZ</value>
  </data>
  <data name="txPDTPDTDeviceID" xml:space="preserve">
    <value>PDTDeviceID</value>
  </data>
  <data name="txPDTRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txPDTRouteID" xml:space="preserve">
    <value>RouteID</value>
  </data>
  <data name="txPDTShelf" xml:space="preserve">
    <value>Shelf</value>
  </data>
  <data name="txPDTTruckShelfOverride" xml:space="preserve">
    <value>TruckShelfOverride</value>
  </data>
  <data name="txSIAlternateAddressFlag" xml:space="preserve">
    <value>AlternateAddressFlag</value>
  </data>
  <data name="txSIBarcodeType" xml:space="preserve">
    <value>BarcodeType</value>
  </data>
  <data name="txSIBeltSide" xml:space="preserve">
    <value>BeltSide</value>
  </data>
  <data name="txSICorrectedAddress" xml:space="preserve">
    <value>Remediation corrected address</value>
  </data>
  <data name="txSIDeliverySeqID" xml:space="preserve">
    <value>DeliverySeqID</value>
  </data>
  <data name="txSIDeliveryTime" xml:space="preserve">
    <value>DeliveryTime</value>
  </data>
  <data name="txSIDeliveryType" xml:space="preserve">
    <value>DeliveryType</value>
  </data>
  <data name="txSIDiversionCode" xml:space="preserve">
    <value>DiversionCode</value>
  </data>
  <data name="txSIHandlingClassType" xml:space="preserve">
    <value>HandlingClassType</value>
  </data>
  <data name="txSIPackageType" xml:space="preserve">
    <value>PackageType</value>
  </data>
  <data name="txSIParkingPlanCodeID" xml:space="preserve">
    <value>ParkingPlanCodeID</value>
  </data>
  <data name="txSIPreprintID" xml:space="preserve">
    <value>PreprintID</value>
  </data>
  <data name="txSIPrimarySort" xml:space="preserve">
    <value>PrimarySort</value>
  </data>
  <data name="txSIResolvedBy" xml:space="preserve">
    <value>ResolvedBy</value>
  </data>
  <data name="txSIRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txSIRoutePlanVersionID" xml:space="preserve">
    <value>RoutePlanVersionID</value>
  </data>
  <data name="txSIShelf" xml:space="preserve">
    <value>Shelf</value>
  </data>
  <data name="txSIShipmentType" xml:space="preserve">
    <value>ShipmentType</value>
  </data>
  <data name="txSISmartSortMode" xml:space="preserve">
    <value>SmartSortMode</value>
  </data>
  <data name="txSITerminal" xml:space="preserve">
    <value>Smart Sort Terminal</value>
  </data>
  <data name="txSITruckShelfOverride" xml:space="preserve">
    <value>TruckShelfOverride</value>
  </data>
  <data name="txSMCreateDateTime" xml:space="preserve">
    <value>Date manifest received</value>
  </data>
  <data name="txSMCurrentAddress" xml:space="preserve">
    <value>Current address</value>
  </data>
  <data name="txSMCurrentAddressSource" xml:space="preserve">
    <value>Current Address source</value>
  </data>
  <data name="txSMDeclaredAddress" xml:space="preserve">
    <value>Declared address</value>
  </data>
  <data name="txSMPurolatorAVInfo" xml:space="preserve">
    <value>Purolator AV Info</value>
  </data>
  <data name="txSMPurolatorAVStatus" xml:space="preserve">
    <value>Purolator AV status</value>
  </data>
  <data name="txSSReason" xml:space="preserve">
    <value>SSStatusReason {0}</value>
  </data>
  <data name="txSSScanTime" xml:space="preserve">
    <value>SSStatusReason {0} Scan time</value>
  </data>
  <data name="txWorksheetTitle" xml:space="preserve">
    <value>Pin History</value>
  </data>
  <data name="txPins" xml:space="preserve">
    <value>PINS</value>
  </data>
  <data name="hdATQInfo" xml:space="preserve">
    <value>Address Triage Queue</value>
  </data>
  <data name="hdPMInfo" xml:space="preserve">
    <value>Pin Master State Code</value>
  </data>
  <data name="txPMComment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="txPMCreatedDateTime" xml:space="preserve">
    <value>Created Time</value>
  </data>
  <data name="txPMLastModifyBy" xml:space="preserve">
    <value>Last Modified By</value>
  </data>
  <data name="txPMLastUpdateDateTime" xml:space="preserve">
    <value>Last Update Time</value>
  </data>
  <data name="txPMStateCode" xml:space="preserve">
    <value>State Code</value>
  </data>
</root>