﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.PinHistory.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class PinHistoryGeneratorResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PinHistoryGeneratorResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.PinHistory.Resources.PinHistoryGeneratorResource", typeof(PinHistoryGeneratorResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Triage Info.
        /// </summary>
        public static string hdATInfo {
            get {
                return ResourceManager.GetString("hdATInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Triage Queue.
        /// </summary>
        public static string hdATQInfo {
            get {
                return ResourceManager.GetString("hdATQInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Courier Manifest Info.
        /// </summary>
        public static string hdCMInfo {
            get {
                return ResourceManager.GetString("hdCMInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDT Info.
        /// </summary>
        public static string hdPDTInfo {
            get {
                return ResourceManager.GetString("hdPDTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin Master State Code.
        /// </summary>
        public static string hdPMInfo {
            get {
                return ResourceManager.GetString("hdPMInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipment manifest info.
        /// </summary>
        public static string hdSMinfo {
            get {
                return ResourceManager.GetString("hdSMinfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smart Sort info.
        /// </summary>
        public static string hdSSInfo {
            get {
                return ResourceManager.GetString("hdSSInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corrected Address.
        /// </summary>
        public static string txATCorrectedAddress {
            get {
                return ResourceManager.GetString("txATCorrectedAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triage escalated by.
        /// </summary>
        public static string txATEscalatedBy {
            get {
                return ResourceManager.GetString("txATEscalatedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triage escalation date/time.
        /// </summary>
        public static string txATEscalatedDT {
            get {
                return ResourceManager.GetString("txATEscalatedDT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triage escalation.
        /// </summary>
        public static string txATEscalation {
            get {
                return ResourceManager.GetString("txATEscalation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triaged by.
        /// </summary>
        public static string txATLastModifyBy {
            get {
                return ResourceManager.GetString("txATLastModifyBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triage date.
        /// </summary>
        public static string txATLastUpdateDateTime {
            get {
                return ResourceManager.GetString("txATLastUpdateDateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route.
        /// </summary>
        public static string txATRoute {
            get {
                return ResourceManager.GetString("txATRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf.
        /// </summary>
        public static string txATShelf {
            get {
                return ResourceManager.GetString("txATShelf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Triage Reason code.
        /// </summary>
        public static string txATTriageReasonCode {
            get {
                return ResourceManager.GetString("txATTriageReasonCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address triage State Code.
        /// </summary>
        public static string txATTriageType {
            get {
                return ResourceManager.GetString("txATTriageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string txCMCity {
            get {
                return ResourceManager.GetString("txCMCity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Courier manifest download source.
        /// </summary>
        public static string txCMCMDownloadSource {
            get {
                return ResourceManager.GetString("txCMCMDownloadSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Courier manifest download time.
        /// </summary>
        public static string txCMCMDownloadTime {
            get {
                return ResourceManager.GetString("txCMCMDownloadTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CustomerName.
        /// </summary>
        public static string txCMCustomerName {
            get {
                return ResourceManager.GetString("txCMCustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CustomerStopID.
        /// </summary>
        public static string txCMCustomerStopID {
            get {
                return ResourceManager.GetString("txCMCustomerStopID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeliverySeqId.
        /// </summary>
        public static string txCMDeliverySeqId {
            get {
                return ResourceManager.GetString("txCMDeliverySeqId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DriverStopId.
        /// </summary>
        public static string txCMDriverStopId {
            get {
                return ResourceManager.GetString("txCMDriverStopId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PackageType.
        /// </summary>
        public static string txCMPackageType {
            get {
                return ResourceManager.GetString("txCMPackageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PostalCode.
        /// </summary>
        public static string txCMPostalCode {
            get {
                return ResourceManager.GetString("txCMPostalCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route.
        /// </summary>
        public static string txCMRoute {
            get {
                return ResourceManager.GetString("txCMRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf.
        /// </summary>
        public static string txCMShelf {
            get {
                return ResourceManager.GetString("txCMShelf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StreetDirection.
        /// </summary>
        public static string txCMStreetDirection {
            get {
                return ResourceManager.GetString("txCMStreetDirection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StreetName.
        /// </summary>
        public static string txCMStreetName {
            get {
                return ResourceManager.GetString("txCMStreetName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StreetNumber.
        /// </summary>
        public static string txCMStreetNumber {
            get {
                return ResourceManager.GetString("txCMStreetNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StreetNumberSuf.
        /// </summary>
        public static string txCMStreetNumberSuf {
            get {
                return ResourceManager.GetString("txCMStreetNumberSuf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StreetType.
        /// </summary>
        public static string txCMStreetType {
            get {
                return ResourceManager.GetString("txCMStreetType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TruckShelfOverride.
        /// </summary>
        public static string txCMTruckShelfOverride {
            get {
                return ResourceManager.GetString("txCMTruckShelfOverride", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UnitNumber.
        /// </summary>
        public static string txCMUnitNumber {
            get {
                return ResourceManager.GetString("txCMUnitNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action {0}.
        /// </summary>
        public static string txPDTAName {
            get {
                return ResourceManager.GetString("txPDTAName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDTDeviceID {0}.
        /// </summary>
        public static string txPDTAPDTDeviceID {
            get {
                return ResourceManager.GetString("txPDTAPDTDeviceID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action {0} ScanDateTimeTZ.
        /// </summary>
        public static string txPDTAScanDateTimeTZ {
            get {
                return ResourceManager.GetString("txPDTAScanDateTimeTZ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDTDeviceID.
        /// </summary>
        public static string txPDTPDTDeviceID {
            get {
                return ResourceManager.GetString("txPDTPDTDeviceID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route.
        /// </summary>
        public static string txPDTRoute {
            get {
                return ResourceManager.GetString("txPDTRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RouteID.
        /// </summary>
        public static string txPDTRouteID {
            get {
                return ResourceManager.GetString("txPDTRouteID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf.
        /// </summary>
        public static string txPDTShelf {
            get {
                return ResourceManager.GetString("txPDTShelf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TruckShelfOverride.
        /// </summary>
        public static string txPDTTruckShelfOverride {
            get {
                return ResourceManager.GetString("txPDTTruckShelfOverride", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PINS.
        /// </summary>
        public static string txPins {
            get {
                return ResourceManager.GetString("txPins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comment.
        /// </summary>
        public static string txPMComment {
            get {
                return ResourceManager.GetString("txPMComment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Time.
        /// </summary>
        public static string txPMCreatedDateTime {
            get {
                return ResourceManager.GetString("txPMCreatedDateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Modified By.
        /// </summary>
        public static string txPMLastModifyBy {
            get {
                return ResourceManager.GetString("txPMLastModifyBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Update Time.
        /// </summary>
        public static string txPMLastUpdateDateTime {
            get {
                return ResourceManager.GetString("txPMLastUpdateDateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State Code.
        /// </summary>
        public static string txPMStateCode {
            get {
                return ResourceManager.GetString("txPMStateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AlternateAddressFlag.
        /// </summary>
        public static string txSIAlternateAddressFlag {
            get {
                return ResourceManager.GetString("txSIAlternateAddressFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BarcodeType.
        /// </summary>
        public static string txSIBarcodeType {
            get {
                return ResourceManager.GetString("txSIBarcodeType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BeltSide.
        /// </summary>
        public static string txSIBeltSide {
            get {
                return ResourceManager.GetString("txSIBeltSide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remediation corrected address.
        /// </summary>
        public static string txSICorrectedAddress {
            get {
                return ResourceManager.GetString("txSICorrectedAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeliverySeqID.
        /// </summary>
        public static string txSIDeliverySeqID {
            get {
                return ResourceManager.GetString("txSIDeliverySeqID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeliveryTime.
        /// </summary>
        public static string txSIDeliveryTime {
            get {
                return ResourceManager.GetString("txSIDeliveryTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeliveryType.
        /// </summary>
        public static string txSIDeliveryType {
            get {
                return ResourceManager.GetString("txSIDeliveryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DiversionCode.
        /// </summary>
        public static string txSIDiversionCode {
            get {
                return ResourceManager.GetString("txSIDiversionCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HandlingClassType.
        /// </summary>
        public static string txSIHandlingClassType {
            get {
                return ResourceManager.GetString("txSIHandlingClassType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PackageType.
        /// </summary>
        public static string txSIPackageType {
            get {
                return ResourceManager.GetString("txSIPackageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ParkingPlanCodeID.
        /// </summary>
        public static string txSIParkingPlanCodeID {
            get {
                return ResourceManager.GetString("txSIParkingPlanCodeID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PreprintID.
        /// </summary>
        public static string txSIPreprintID {
            get {
                return ResourceManager.GetString("txSIPreprintID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PrimarySort.
        /// </summary>
        public static string txSIPrimarySort {
            get {
                return ResourceManager.GetString("txSIPrimarySort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ResolvedBy.
        /// </summary>
        public static string txSIResolvedBy {
            get {
                return ResourceManager.GetString("txSIResolvedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Route.
        /// </summary>
        public static string txSIRoute {
            get {
                return ResourceManager.GetString("txSIRoute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoutePlanVersionID.
        /// </summary>
        public static string txSIRoutePlanVersionID {
            get {
                return ResourceManager.GetString("txSIRoutePlanVersionID", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf.
        /// </summary>
        public static string txSIShelf {
            get {
                return ResourceManager.GetString("txSIShelf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShipmentType.
        /// </summary>
        public static string txSIShipmentType {
            get {
                return ResourceManager.GetString("txSIShipmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SmartSortMode.
        /// </summary>
        public static string txSISmartSortMode {
            get {
                return ResourceManager.GetString("txSISmartSortMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smart Sort Terminal.
        /// </summary>
        public static string txSITerminal {
            get {
                return ResourceManager.GetString("txSITerminal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TruckShelfOverride.
        /// </summary>
        public static string txSITruckShelfOverride {
            get {
                return ResourceManager.GetString("txSITruckShelfOverride", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date manifest received.
        /// </summary>
        public static string txSMCreateDateTime {
            get {
                return ResourceManager.GetString("txSMCreateDateTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current address.
        /// </summary>
        public static string txSMCurrentAddress {
            get {
                return ResourceManager.GetString("txSMCurrentAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Address source.
        /// </summary>
        public static string txSMCurrentAddressSource {
            get {
                return ResourceManager.GetString("txSMCurrentAddressSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declared address.
        /// </summary>
        public static string txSMDeclaredAddress {
            get {
                return ResourceManager.GetString("txSMDeclaredAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purolator AV Info.
        /// </summary>
        public static string txSMPurolatorAVInfo {
            get {
                return ResourceManager.GetString("txSMPurolatorAVInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purolator AV status.
        /// </summary>
        public static string txSMPurolatorAVStatus {
            get {
                return ResourceManager.GetString("txSMPurolatorAVStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSStatusReason {0}.
        /// </summary>
        public static string txSSReason {
            get {
                return ResourceManager.GetString("txSSReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSStatusReason {0} Scan time.
        /// </summary>
        public static string txSSScanTime {
            get {
                return ResourceManager.GetString("txSSScanTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin History.
        /// </summary>
        public static string txWorksheetTitle {
            get {
                return ResourceManager.GetString("txWorksheetTitle", resourceCulture);
            }
        }
    }
}
