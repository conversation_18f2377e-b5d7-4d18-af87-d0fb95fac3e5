﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="hdATInfo" xml:space="preserve">
    <value>Information sur le triage des adresses</value>
  </data>
  <data name="hdCMInfo" xml:space="preserve">
    <value>Information sur le manifeste des courriers</value>
  </data>
  <data name="hdPDTInfo" xml:space="preserve">
    <value>Information sur le terminal de données portable</value>
  </data>
  <data name="hdSMinfo" xml:space="preserve">
    <value>Information sur le manifeste de l'envoi</value>
  </data>
  <data name="hdSSInfo" xml:space="preserve">
    <value>Information sur le tri intelligent</value>
  </data>
  <data name="txATCorrectedAddress" xml:space="preserve">
    <value>Adresse corrigée</value>
  </data>
  <data name="txATEscalatedBy" xml:space="preserve">
    <value>Triage transféré par</value>
  </data>
  <data name="txATEscalatedDT" xml:space="preserve">
    <value>Date/heure du transfert du triage</value>
  </data>
  <data name="txATEscalation" xml:space="preserve">
    <value>Transfert du triage</value>
  </data>
  <data name="txATLastModifyBy" xml:space="preserve">
    <value>Trié par</value>
  </data>
  <data name="txATLastUpdateDateTime" xml:space="preserve">
    <value>Date de triage</value>
  </data>
  <data name="txATRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txATShelf" xml:space="preserve">
    <value>Étagère</value>
  </data>
  <data name="txATTriageReasonCode" xml:space="preserve">
    <value>Code de raison du triage</value>
  </data>
  <data name="txATTriageType" xml:space="preserve">
    <value>Code d'état du triage des adresses</value>
  </data>
  <data name="txCMCity" xml:space="preserve">
    <value>Ville</value>
  </data>
  <data name="txCMCMDownloadSource" xml:space="preserve">
    <value>Source de téléchargement du manifeste des courriers</value>
  </data>
  <data name="txCMCMDownloadTime" xml:space="preserve">
    <value>Heure de téléchargement du manifeste des courriers</value>
  </data>
  <data name="txCMCustomerName" xml:space="preserve">
    <value>Nom du client</value>
  </data>
  <data name="txCMCustomerStopID" xml:space="preserve">
    <value>Code d'arrêt du client</value>
  </data>
  <data name="txCMDeliverySeqId" xml:space="preserve">
    <value>Codeséq livraison</value>
  </data>
  <data name="txCMDriverStopId" xml:space="preserve">
    <value>Codearr conducteur</value>
  </data>
  <data name="txCMPackageType" xml:space="preserve">
    <value>Type de colis</value>
  </data>
  <data name="txCMPostalCode" xml:space="preserve">
    <value>Code postal</value>
  </data>
  <data name="txCMRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txCMShelf" xml:space="preserve">
    <value>Étagère</value>
  </data>
  <data name="txCMStreetDirection" xml:space="preserve">
    <value>Direction de rue</value>
  </data>
  <data name="txCMStreetName" xml:space="preserve">
    <value>Nom de rue</value>
  </data>
  <data name="txCMStreetNumber" xml:space="preserve">
    <value>Numéro de rue</value>
  </data>
  <data name="txCMStreetNumberSuf" xml:space="preserve">
    <value>Suffno de rue</value>
  </data>
  <data name="txCMStreetType" xml:space="preserve">
    <value>Type de rue</value>
  </data>
  <data name="txCMTruckShelfOverride" xml:space="preserve">
    <value>Outre passé tag camion</value>
  </data>
  <data name="txCMUnitNumber" xml:space="preserve">
    <value>Numéro d'unité</value>
  </data>
  <data name="txPDTAName" xml:space="preserve">
    <value>Action {0}</value>
  </data>
  <data name="txPDTAPDTDeviceID" xml:space="preserve">
    <value>Code terminal donnéesportable {0}</value>
  </data>
  <data name="txPDTAScanDateTimeTZ" xml:space="preserve">
    <value>Action {0} Date heure lecture</value>
  </data>
  <data name="txPDTPDTDeviceID" xml:space="preserve">
    <value>Code terminal donnéesportable</value>
  </data>
  <data name="txPDTRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txPDTRouteID" xml:space="preserve">
    <value>Code de route</value>
  </data>
  <data name="txPDTShelf" xml:space="preserve">
    <value>Étagère</value>
  </data>
  <data name="txPDTTruckShelfOverride" xml:space="preserve">
    <value>Outre passé tag camion</value>
  </data>
  <data name="txSIAlternateAddressFlag" xml:space="preserve">
    <value>Avis autre adresse</value>
  </data>
  <data name="txSIBarcodeType" xml:space="preserve">
    <value>Typecode à barres</value>
  </data>
  <data name="txSIBeltSide" xml:space="preserve">
    <value>Côté convoyeur</value>
  </data>
  <data name="txSICorrectedAddress" xml:space="preserve">
    <value>Adresse corrigée pendant la résolution</value>
  </data>
  <data name="txSIDeliverySeqID" xml:space="preserve">
    <value>Codeséq livraison</value>
  </data>
  <data name="txSIDeliveryTime" xml:space="preserve">
    <value>Heure de livraison</value>
  </data>
  <data name="txSIDeliveryType" xml:space="preserve">
    <value>Type de livraison</value>
  </data>
  <data name="txSIDiversionCode" xml:space="preserve">
    <value>Code de diversion</value>
  </data>
  <data name="txSIHandlingClassType" xml:space="preserve">
    <value>Type de classe de manutention</value>
  </data>
  <data name="txSIPackageType" xml:space="preserve">
    <value>Type de colis</value>
  </data>
  <data name="txSIParkingPlanCodeID" xml:space="preserve">
    <value>Code plan de stationnement</value>
  </data>
  <data name="txSIPreprintID" xml:space="preserve">
    <value>Code préimprimés</value>
  </data>
  <data name="txSIPrimarySort" xml:space="preserve">
    <value>Tri principal</value>
  </data>
  <data name="txSIResolvedBy" xml:space="preserve">
    <value>Résolu par</value>
  </data>
  <data name="txSIRoute" xml:space="preserve">
    <value>Route</value>
  </data>
  <data name="txSIRoutePlanVersionID" xml:space="preserve">
    <value>Code de version du plan de route</value>
  </data>
  <data name="txSIShelf" xml:space="preserve">
    <value>Étagère</value>
  </data>
  <data name="txSIShipmentType" xml:space="preserve">
    <value>Type d'envoi</value>
  </data>
  <data name="txSISmartSortMode" xml:space="preserve">
    <value>Mode tri intelligent</value>
  </data>
  <data name="txSITerminal" xml:space="preserve">
    <value>Dépôt de tri intelligent</value>
  </data>
  <data name="txSITruckShelfOverride" xml:space="preserve">
    <value>Outre passé tag camion</value>
  </data>
  <data name="txSMCreateDateTime" xml:space="preserve">
    <value>Date de réception du manifeste</value>
  </data>
  <data name="txSMCurrentAddress" xml:space="preserve">
    <value>Adresse actuelle</value>
  </data>
  <data name="txSMCurrentAddressSource" xml:space="preserve">
    <value>Source de l'adresse actuelle</value>
  </data>
  <data name="txSMDeclaredAddress" xml:space="preserve">
    <value>Adresse déclarée</value>
  </data>
  <data name="txSMPurolatorAVInfo" xml:space="preserve">
    <value>Information VA de Purolator</value>
  </data>
  <data name="txSMPurolatorAVStatus" xml:space="preserve">
    <value>État VA de Purolator</value>
  </data>
  <data name="txSSReason" xml:space="preserve">
    <value>Raison de l'état du tri intelligent {0}</value>
  </data>
  <data name="txSSScanTime" xml:space="preserve">
    <value>Raison de l'état du tri intelligent {0} heure de lecture</value>
  </data>
  <data name="txWorksheetTitle" xml:space="preserve">
    <value>Historique du NIC</value>
  </data>
  <data name="txPins" xml:space="preserve">
    <value>NIC</value>
  </data>
  <data name="hdATQInfo" xml:space="preserve">
    <value>File de triage des adresses</value>
  </data>
  <data name="hdPMInfo" xml:space="preserve">
    <value>Code d'état maître du NIC</value>
  </data>
  <data name="txPMComment" xml:space="preserve">
    <value>Commentaires</value>
  </data>
  <data name="txPMCreatedDateTime" xml:space="preserve">
    <value>Heure de création</value>
  </data>
  <data name="txPMLastModifyBy" xml:space="preserve">
    <value>Dernière modification par</value>
  </data>
  <data name="txPMLastUpdateDateTime" xml:space="preserve">
    <value>Heure de la dernière mise à jour</value>
  </data>
  <data name="txPMStateCode" xml:space="preserve">
    <value>Code d'état</value>
  </data>
</root>