﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartSort.Common;
using SmartSort.Business.Entities.PinHistory;

namespace Purolator.SmartSort.PinHistory
{
    public class PinHistoryRenderer: IRenderer
    {

        private PinHistoryGenerator generator;
        private PinHistoryList pinHistory;

        public PinHistoryRenderer(PinHistoryGenerator gen, PinHistoryList pinHist)
        {
            generator = gen;
            pinHistory = pinHist;
        }

        public void Render(Stream output)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                generator.GeneratePinHistory(ms, pinHistory);
                byte[] arr = ms.ToArray();
                output.Write(arr, 0, arr.Length);
            }
            
        }
    }
}
