﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2D713C48-C831-4F41-B87F-46D84B60EBF8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Purolator.SmartSort.PinHistory</RootNamespace>
    <AssemblyName>Purolator.SmartSort.PinHistory</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="MemberHelper.cs" />
    <Compile Include="PinHistoryGenerator.cs" />
    <Compile Include="PinHistoryRecordDefinition.cs" />
    <Compile Include="PinHistoryRenderer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resources\PinHistoryGeneratorResource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PinHistoryGeneratorResource.resx</DependentUpon>
    </Compile>
    <Compile Include="Resources\PinHistoryGeneratorResource.fr-ca.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PinHistoryGeneratorResource.fr-ca.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenXmlPackaging\OpenXmlPackaging.csproj">
      <Project>{dad3387a-470a-4484-9427-a8c8faec6c0f}</Project>
      <Name>OpenXmlPackaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Business.Entities\SmartSort.Business.Entities.csproj">
      <Project>{5662ba8a-f770-420b-a01c-a388b211f25d}</Project>
      <Name>SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Common\SmartSort.Common.csproj">
      <Project>{4a28b5ff-887c-4a9a-b054-5e88f1119ab3}</Project>
      <Name>SmartSort.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\PinHistoryGeneratorResource.fr-ca.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PinHistoryGeneratorResource.fr-ca.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\PinHistoryGeneratorResource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PinHistoryGeneratorResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>