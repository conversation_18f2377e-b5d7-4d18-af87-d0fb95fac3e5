﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Globalization;
using System.Xml;
using System.Xml.XPath;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.Rendering;
using System.Diagnostics;
using PdfSharp.Pdf;
using SmartSort.Common;
using SmartSort.Business.Entities;

namespace Purolator.SmartSort.CourierManifestRendering
{
    public class CourierManifestRenderer : IRenderer
    {
        #region Private Variables
        private CourierManifestPDFForm courierManifestPDFForm = new CourierManifestPDFForm();
        private List<CourierManifest> courierManifests = null;
        private string reportLogoPath = String.Empty;
        #endregion

        #region Public Properties
        public CourierManifestPDFForm CourierManifestPDFForm
        {
            get { return courierManifestPDFForm; }
        }
        #endregion

        #region Public Methods
        public void Render(Stream output)
        {
            Document document = courierManifestPDFForm.CreateDocument(reportLogoPath, courierManifests);
            document.UseCmykColor = true;
            
            PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer(false, PdfFontEmbedding.Always)
            {
                Document = document
            };

            pdfRenderer.RenderDocument();

            using (MemoryStream ms = new MemoryStream())
            {
                pdfRenderer.PdfDocument.Save(ms, false);
                byte[] arr = ms.ToArray();
                output.Write(arr, 0, arr.Length);
            }
        }
        #endregion

        #region Public Constructors
        public CourierManifestRenderer(string reportLogoPath, List<CourierManifest> courierManifests)
        {
            this.reportLogoPath = reportLogoPath;
            this.courierManifests = courierManifests;
        }
        #endregion
    }
}
