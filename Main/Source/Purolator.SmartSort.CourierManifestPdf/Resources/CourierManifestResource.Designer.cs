﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.34209
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Purolator.SmartSort.CourierManifestPdf.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CourierManifestResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CourierManifestResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Purolator.SmartSort.CourierManifestPdf.Resources.CourierManifestResource", typeof(CourierManifestResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boxes.
        /// </summary>
        public static string Boxes {
            get {
                return ResourceManager.GetString("Boxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Civic #
        ///(Suffix).
        /// </summary>
        public static string CivicSuffix {
            get {
                return ResourceManager.GetString("CivicSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Courier Manifest.
        /// </summary>
        public static string CMTitle {
            get {
                return ResourceManager.GetString("CMTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to COS.
        /// </summary>
        public static string Cos {
            get {
                return ResourceManager.GetString("Cos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        public static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Printed:   .
        /// </summary>
        public static string DateCreated {
            get {
                return ResourceManager.GetString("DateCreated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateManifested:.
        /// </summary>
        public static string DateManifested {
            get {
                return ResourceManager.GetString("DateManifested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DG.
        /// </summary>
        public static string Dg {
            get {
                return ResourceManager.GetString("Dg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EVENING.
        /// </summary>
        public static string Evening {
            get {
                return ResourceManager.GetString("Evening", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heavy Weight.
        /// </summary>
        public static string HeavyWeight {
            get {
                return ResourceManager.GetString("HeavyWeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail.
        /// </summary>
        public static string Mail {
            get {
                return ResourceManager.GetString("Mail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 9AM.
        /// </summary>
        public static string NineAM {
            get {
                return ResourceManager.GetString("NineAM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data is not available for the specified report criteria..
        /// </summary>
        public static string NoData {
            get {
                return ResourceManager.GetString("NoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 12PM.
        /// </summary>
        public static string Noon {
            get {
                return ResourceManager.GetString("Noon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to # of
        ///Pieces.
        /// </summary>
        public static string NoPieces {
            get {
                return ResourceManager.GetString("NoPieces", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package
        ///Location.
        /// </summary>
        public static string PackageLocation {
            get {
                return ResourceManager.GetString("PackageLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROUTE NUMBER.
        /// </summary>
        public static string RouteNumber {
            get {
                return ResourceManager.GetString("RouteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sequence ID.
        /// </summary>
        public static string Seqid {
            get {
                return ResourceManager.GetString("Seqid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        public static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shelf.
        /// </summary>
        public static string Shelf {
            get {
                return ResourceManager.GetString("Shelf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Street
        ///(Direction).
        /// </summary>
        public static string StreetDirection {
            get {
                return ResourceManager.GetString("StreetDirection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suite/Unit.
        /// </summary>
        public static string Suite {
            get {
                return ResourceManager.GetString("Suite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10:30AM.
        /// </summary>
        public static string TenThirtyAM {
            get {
                return ResourceManager.GetString("TenThirtyAM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terminal .
        /// </summary>
        public static string Terminal {
            get {
                return ResourceManager.GetString("Terminal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Customer Stops:    .
        /// </summary>
        public static string TotalCustomerStop {
            get {
                return ResourceManager.GetString("TotalCustomerStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Pieces:        .
        /// </summary>
        public static string TotalPieces {
            get {
                return ResourceManager.GetString("TotalPieces", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Vehicle Stops:         .
        /// </summary>
        public static string TotalVehicleStop {
            get {
                return ResourceManager.GetString("TotalVehicleStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unclassified.
        /// </summary>
        public static string Unclassified {
            get {
                return ResourceManager.GetString("Unclassified", resourceCulture);
            }
        }
    }
}
