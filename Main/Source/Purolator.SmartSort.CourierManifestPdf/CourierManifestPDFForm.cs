﻿using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using SmartSort.Business.Entities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Web;
using Purolator.SmartSort.CourierManifestPdf.Resources;


namespace Purolator.SmartSort.CourierManifestRendering
{
    public struct TableAttributes
    {
        #region Public Properties
        public string Text { get; set; }
        public ParagraphAlignment ParagraphAlignment { get; set; }
        public VerticalAlignment VerticalAlignment { get; set; }
        public Unit Width { get; set; }
        #endregion
    }

    public class CourierManifestPDFForm
    {
        #region Private Variables
        private readonly static Color DefaultTableColor = new Color(235, 240, 249);
        private readonly static Color DefaultTableBorderColor = new Color(81, 125, 192);
        private readonly static Color DefaultAltCellColor = new Color(196, 196, 196);

        private readonly List<TableAttributes> SummaryManifestTableAttributes = new List<TableAttributes>()
        {
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",   Text = CourierManifestResource.Seqid } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",   Text = CourierManifestResource.NineAM  } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2.1cm", Text = CourierManifestResource.TenThirtyAM } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2.1cm", Text = CourierManifestResource.Noon } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",   Text = CourierManifestResource.Cos } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",   Text = CourierManifestResource.Dg } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "3cm",   Text = CourierManifestResource.Evening } },
             { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "3cm",   Text = CourierManifestResource.HeavyWeight } },
        };

        private readonly List<TableAttributes> PackageManifestTableAttributes = new List<TableAttributes>()
        {
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.8cm",  Text = CourierManifestResource.Seqid  } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.2cm",  Text = CourierManifestResource.Shelf } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.8cm",  Text = CourierManifestResource.PackageLocation } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.5cm",  Text = CourierManifestResource.Service } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "4cm",    Text = CourierManifestResource.Customer } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",    Text = CourierManifestResource.CivicSuffix } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "4.2cm",  Text = CourierManifestResource.StreetDirection } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.8cm",  Text = CourierManifestResource.Suite } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "3cm",    Text = CourierManifestResource.City } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.5cm",  Text = CourierManifestResource.NoPieces } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.5cm",  Text = CourierManifestResource.Mail } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "1.5cm",  Text = CourierManifestResource.Boxes } },
            { new TableAttributes() { ParagraphAlignment = ParagraphAlignment.Center, VerticalAlignment = VerticalAlignment.Center, Width = "2cm",    Text = CourierManifestResource.Unclassified } }
        };
        #endregion

        #region Private Methods 
        private Table AddTable(Section section, bool isSummary = false)
        {
            TextFrame addressFrame = null;
            Table table = null;

            if (isSummary)
            { 
                addressFrame = section.AddTextFrame();
                addressFrame.Width = "16.0cm";
                addressFrame.Left = ShapePosition.Center;
                addressFrame.MarginBottom = 0;
                addressFrame.Height = "2cm";
            }

            table = isSummary ? addressFrame.AddTable() : section.AddTable();
            table.Style = "Table";
            table.Borders.Color = DefaultTableBorderColor;
            table.Borders.Width = 0.25;
            table.Borders.Left.Width = 0.5;
            table.Borders.Right.Width = 0.5;
            table.Rows.LeftIndent = 0; 
            return table;
        }

        private Row AddTableColumn(Table table)
        {
            Row row = table.AddRow();
            row.HeadingFormat = true;
            row.Format.Alignment = ParagraphAlignment.Center;
            row.Format.Font.Bold = true;
            row.Shading.Color = DefaultTableColor;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void AddTableColumn(Table table, TableAttributes tableAttributes)
        {
            Column column = table.AddColumn(tableAttributes.Width);
            column.Format.Alignment = tableAttributes.ParagraphAlignment;
        }

        private void FillTableColumn(Cell cell, TableAttributes tableAttributes)
        {
            cell.AddParagraph(tableAttributes.Text);
            cell.Format.Alignment = tableAttributes.ParagraphAlignment;
            cell.VerticalAlignment = tableAttributes.VerticalAlignment;
        }

        private Row AddTableRow(Table table)
        {
            Row row = table.AddRow();
            row.TopPadding = 1.5;
            row.Format.Font.Size = 9;
            row.TopPadding = 2;
            row.BottomPadding = 2;
            return row;
        }

        private void ApplyDocumentStyles(Document document)
        {
            Style style = document.Styles["Normal"];
            style.Font.Name = "Arial";
            style = document.Styles[StyleNames.Header];
            style.ParagraphFormat.AddTabStop("16cm", TabAlignment.Right);

            style = document.Styles[StyleNames.Footer];
            style.ParagraphFormat.AddTabStop("8cm", TabAlignment.Center);

            style = document.Styles.AddStyle("Table", "Normal");
            style.Font.Name = "Arial";
            style.Font.Size = 9;

            style = document.Styles.AddStyle("Reference", "Normal");
            style.ParagraphFormat.SpaceBefore = "5mm";
            style.ParagraphFormat.SpaceAfter = "5mm";
            style.ParagraphFormat.TabStops.AddTabStop("16cm", TabAlignment.Right);
        }

        private Section AddDocumentSection(string logoPath, Document document,CourierManifestSummary courierManifestSummary)
        {
            string creationDate = courierManifestSummary.CreationDate.ToString(@"MM-dd-yyyy h:mm:ss tt", CultureInfo.InvariantCulture);
            string manifestedDate = courierManifestSummary.ManifestedDate.ToString(@"MM-dd-yyyy h:mm:ss tt", CultureInfo.InvariantCulture);
            Section section = document.AddSection();
            section.Document.DefaultPageSetup.LeftMargin = "1.0cm";
            section.Document.DefaultPageSetup.RightMargin = "1.0cm";

            Image image = section.Headers.Primary.AddImage(logoPath);
            image.Height = "0.5cm";
            image.LockAspectRatio = true;
            image.RelativeVertical = RelativeVertical.Line;
            image.RelativeHorizontal = RelativeHorizontal.Margin;
            image.Top = ShapePosition.Top;
            image.Left = ShapePosition.Right;
            image.WrapFormat.Style = WrapStyle.Through;
            
            TextFrame textFrame = section.Headers.Primary.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "7.0cm";
            textFrame.Left = ShapePosition.Left;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Top = "1.3cm";
            textFrame.RelativeVertical = RelativeVertical.Page;

            Paragraph paragraph = textFrame.AddParagraph();
            paragraph.AddFormattedText(CourierManifestResource.DateCreated, new Font() { Size = 9, Bold = true });
            paragraph.AddFormattedText(creationDate, new Font() { Size = 9, Bold = false });
            paragraph.AddLineBreak();
            paragraph.AddFormattedText(CourierManifestResource.DateManifested, new Font() { Size = 9, Bold = true });
            paragraph.AddFormattedText(manifestedDate, new Font() { Size = 9, Bold = false });
  
            paragraph = section.Headers.Primary.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddFormattedText(CourierManifestResource.Terminal, new Font() { Size = 9, Bold = false });
            paragraph.AddFormattedText(courierManifestSummary.TerminalName + "\n\n", TextFormat.Bold);

            paragraph = section.Headers.Primary.AddParagraph();
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddFormattedText(courierManifestSummary.Route, new Font() { Size = 11, Bold = true });
            
            paragraph = section.Headers.Primary.AddParagraph();

            paragraph.AddText(CourierManifestResource.RouteNumber);
            paragraph.Format.Font.Size = 8;
            paragraph.Format.Font.Bold = false;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            section.Headers.Primary.Section.PageSetup.TopMargin = "3.3cm";

            paragraph = section.Footers.Primary.AddParagraph();
            paragraph.AddText(CourierManifestResource.Terminal + courierManifestSummary.TerminalName);
            paragraph.Format.Font.Size = 8;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            paragraph = section.Footers.Primary.AddParagraph();
            paragraph.Format.Font.Size = 9;
            paragraph.Format.Alignment = ParagraphAlignment.Right;
            paragraph.AddText("Page ");
            paragraph.AddPageField();
            paragraph.AddText(" of ");
            paragraph.AddNumPagesField();

            return section;
        }

        private void AddDataNotAvailableRegion(Document document)
        {
            Section section = document.LastSection;
            TextFrame addressFrame = section.AddTextFrame();
            addressFrame.Top = "5.9cm";
            addressFrame.Width = " 10cm";
            addressFrame.Left = "9cm";
            Paragraph paragraph = addressFrame.AddParagraph();
            paragraph.Format.Font.Size = 12;
            paragraph.Format.Alignment = ParagraphAlignment.Center;
            paragraph.AddText(CourierManifestResource.NoData);
        }


        private void AddSummaryInfo(Section section, CourierManifestSummary courierManifestSummary)
        {
            TextFrame textFrame = section.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "7.0cm";
            textFrame.Left = ShapePosition.Left;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Left = "5.8cm";
            textFrame.Top = "2.1cm";
            textFrame.RelativeVertical = RelativeVertical.Page;

            Paragraph paragraph = textFrame.AddParagraph();

            paragraph.AddFormattedText(CourierManifestResource.TotalVehicleStop, new Font() { Size = 9, Bold = true });
            paragraph.AddFormattedText(courierManifestSummary.TotalVehicleStops.ToString(), new Font() { Size = 9, Bold = false });
            paragraph.AddLineBreak();
            paragraph.AddFormattedText(CourierManifestResource.TotalCustomerStop, new Font() { Size = 9, Bold = true });
            paragraph.AddFormattedText(courierManifestSummary.TotalCustomerStops.ToString(), new Font() { Size = 9, Bold = false });
            //paragraph.AddLineBreak();
            
            textFrame = section.AddTextFrame();
            textFrame.Height = "3.0cm";
            textFrame.Width = "7.0cm";
            textFrame.Left = ShapePosition.Left;
            textFrame.RelativeHorizontal = RelativeHorizontal.Margin;
            textFrame.Left = "18.6cm";
            textFrame.Top = "2.1cm";
            textFrame.RelativeVertical = RelativeVertical.Page;

            paragraph = textFrame.AddParagraph();
            paragraph.AddFormattedText(CourierManifestResource.TotalPieces, new Font() { Size = 9, Bold = true });
            paragraph.AddFormattedText(courierManifestSummary.TotalPieces.ToString(), new Font() { Size = 9, Bold = false });
        }

        private void AddSummaryManifestTable(Section section, List<CourierManifestSummaryItem> courierManifestSummaryItems)
        {
            Table table = AddTable(section, true);
            table.Format.Alignment = ParagraphAlignment.Center;

            foreach (var tableAttributes in SummaryManifestTableAttributes)
            {
                AddTableColumn(table, tableAttributes);
            }

            Row row = AddTableColumn(table);

            for (int i = 0; i < SummaryManifestTableAttributes.Count; i++)
            {
                FillTableColumn(row.Cells[i], SummaryManifestTableAttributes[i]);
            }

            FillSummaryManifestTable(courierManifestSummaryItems, table);
        }

        private void FillSummaryManifestTable(List<CourierManifestSummaryItem> courierManifestSummaryItems, Table table)
        {
            foreach (var manifestItem in courierManifestSummaryItems)
            {
                Row row = AddTableRow(table);

                row.Cells[0].AddParagraph(manifestItem.Type);
                row.Cells[1].AddParagraph(manifestItem.S0900.ToString());
                row.Cells[2].AddParagraph(manifestItem.S1030.ToString());
                row.Cells[3].AddParagraph(manifestItem.S1200.ToString());
                row.Cells[4].AddParagraph(manifestItem.SCOS.ToString());
                row.Cells[5].AddParagraph(manifestItem.SDG.ToString());
                row.Cells[6].AddParagraph(manifestItem.SENV.ToString());
                row.Cells[7].AddParagraph(manifestItem.SHW.ToString());
            }
        }

        private void AddPackageManifestTable(Section section, List<CourierManifestItem> courierManifestItems)
        {
            Table table = AddTable(section);

            foreach (var tableAttributes in PackageManifestTableAttributes)
            {
                AddTableColumn(table, tableAttributes);
            }

            Row row = AddTableColumn(table);

            for (int i = 0; i < PackageManifestTableAttributes.Count; i ++)
            {
                FillTableColumn(row.Cells[i],  PackageManifestTableAttributes[i]);
            }

            FillPackageManifestTable(courierManifestItems, table);
        }

        private void FillPackageManifestTable(List<CourierManifestItem> courierManifestItems, Table table)
        {
            bool isUseAltCellColor = false;
            string lastShelf = String.Empty;

            foreach (var manifestItem in courierManifestItems)
            {
                Row row = AddTableRow(table);
                bool isPRGrouping = manifestItem.IsPRGrouping;
                string streetName = !String.IsNullOrWhiteSpace(manifestItem.Street) ? CultureInfo.CurrentCulture.TextInfo.ToTitleCase(manifestItem.Street.ToLower()) : String.Empty;
                string city = !String.IsNullOrWhiteSpace(manifestItem.City) ? CultureInfo.CurrentCulture.TextInfo.ToTitleCase(manifestItem.City.ToLower()) : String.Empty;
                string shelf = manifestItem.Shelf;

                if (!isPRGrouping && !String.IsNullOrWhiteSpace(lastShelf) && !String.Equals(lastShelf, shelf, StringComparison.OrdinalIgnoreCase))
                {
                    isUseAltCellColor = !isUseAltCellColor;
                }

                lastShelf = shelf;
    
                AddCellParagraph(row.Cells[0], manifestItem.DeliverySequenceId.ToString(), isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[1], shelf,isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[2], manifestItem.PackageLocation, false, isUseAltCellColor);
                AddCellParagraph(row.Cells[3], manifestItem.ServiceTime, false, isUseAltCellColor);
                AddCellParagraph(row.Cells[4], manifestItem.CustomerName, isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[5], manifestItem.StreetNumber, isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[6], streetName, isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[7], manifestItem.UnitNumber, isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[8], city, isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[9], manifestItem.Pieces.ToString(), isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[10], manifestItem.Mail.ToString(), isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[11], manifestItem.Boxes.ToString(), isPRGrouping, isUseAltCellColor);
                AddCellParagraph(row.Cells[12], manifestItem.Unclassified.ToString(), isPRGrouping, isUseAltCellColor);
            }
        }

        private Paragraph AddCellParagraph(Cell cell, string text, bool isBold, bool isUseAltCellColor = false)
        {
           Paragraph paragraph = cell.AddParagraph(text);
           paragraph.Format.Font.Bold = isBold;

           if (isUseAltCellColor)
           {
               cell.Shading.Color = DefaultAltCellColor;
           }
           return paragraph; 
        }
        #endregion

        #region Public Methods
        public Document CreateDocument(string logoPath, List<CourierManifest> courierManifests)
        {
            Document document = new Document();
            document.Info.Title = CourierManifestResource.CMTitle;
            document.Info.Subject = CourierManifestResource.CMTitle;
            document.Info.Author = "Purolator";
            document.DefaultPageSetup.PageFormat = PageFormat.A4;
            document.DefaultPageSetup.Orientation = Orientation.Landscape;
           
            //document.DefaultPageSetup.TopMargin = "1cm";
            //document.DefaultPageSetup.BottomMargin = "1cm";
            //document.DefaultPageSetup.Pag = PdfSharp.PageSize.Letter;

            ApplyDocumentStyles(document);

            if (courierManifests != null && courierManifests.Count > 0)
            {
                foreach (var courierManifest in courierManifests)
                {
                    Section section = AddDocumentSection(logoPath, document, courierManifest.ManifestSummary);

                    if (courierManifest != null && courierManifest.ManifestItems != null && courierManifest.ManifestItems.Count > 0)
                    {
                        AddSummaryInfo(section, courierManifest.ManifestSummary);
                        AddSummaryManifestTable(section, courierManifest.ManifestSummary.SummaryItems);
                        AddPackageManifestTable(section, courierManifest.ManifestItems);
                    }
                    else
                    {
                        AddDataNotAvailableRegion(document);
                    }
                }
            }
          
            return document;
        }
        #endregion
    }
}
