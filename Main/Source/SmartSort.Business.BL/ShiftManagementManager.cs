﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Linq;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;

namespace SmartSort.Business.Components
{
    public class ShiftManagementManager
    {
        #region Private Variables
        private readonly ShiftManagementRepository shiftManagementRepository = new ShiftManagementRepository(Utility.GetDatabaseConnectionName());
        #endregion

        #region Public Methods
        public bool InitiateAMShift(string terminalName, DateTimeOffset dateTimeOffset, string userName, DateTime shiftDate)
        {
            bool isShiftStarted = shiftManagementRepository.InitiateAMShift(terminalName, dateTimeOffset, userName, shiftDate);            
            return isShiftStarted;
        }

        public ShiftManagement GetShiftManagementInfo(string terminalName, bool frozen)
        {
            ShiftManagement shiftManagement = new ShiftManagement();
            ShiftStatus shiftStatus = GetShiftStatus(terminalName);
            shiftManagement.IsShiftStarted = shiftStatus.IsShiftStarted;
            shiftManagement.PinResetPieces = shiftStatus.ResetPinCount;
            shiftManagement.TerminalName = terminalName;
            shiftManagement.SortPlanName = shiftManagementRepository.GetActiveSortPlan(terminalName);

            shiftManagement.AutoStartTime = shiftManagementRepository.GetAutoStartTime(terminalName);
            shiftManagement.PreviousMoveDates = shiftManagementRepository.GetPreviousMoveDates(terminalName);

            if (shiftStatus.ResetPinCount == 0)
            {
                dynamic vehicleVolumeReportSummary = GenerateVehicleVolumeReportSummary(terminalName, frozen); 
                if (vehicleVolumeReportSummary != null)
                {                
                    shiftManagement.VehicleVolumeReports = vehicleVolumeReportSummary.VehicleVolumeReports;
                    shiftManagement.ShiftDate = vehicleVolumeReportSummary.ShiftDate;
                    shiftManagement.TotalMRStops = vehicleVolumeReportSummary.MRStops;
                    shiftManagement.TotalMRPieces = vehicleVolumeReportSummary.MRPieces;
                    shiftManagement.TotalProjectedStops = vehicleVolumeReportSummary.ProjectedStops;
                    shiftManagement.TotalProjectedPieces = vehicleVolumeReportSummary.ProjectedPieces;
                    shiftManagement.TotalActualStops = vehicleVolumeReportSummary.ActualStops;
                    shiftManagement.TotalActualPieces = vehicleVolumeReportSummary.ActualPieces;
               
               
                }
            }
            return shiftManagement;
        }

        public object[] GetRoutesAndShelves(string terminalName)
        {
            var dataTableCollection = shiftManagementRepository.GetRoutesAndShelves(terminalName);
            object[] routesAndShelves = null;

            if (dataTableCollection != null && dataTableCollection.Count >= 2)
            {
                var routes = dataTableCollection[0].AsEnumerable().Select(x => x[0].ToString()).ToList();
                var shelves = dataTableCollection[1].AsEnumerable().Select(x => x[0].ToString()).ToList();
                routesAndShelves = new object[] { routes, shelves };
            }
            return routesAndShelves;
        }
      

        public List<CustomerStop> GetCustomerStops(string terminalName, string sourceRoute, string sourceShelf)
        {
            var preShiftLoadBalancing = new PreShiftLoadBalancing()
            {
                TerminalName = terminalName,
                SourceRoute = sourceRoute,
                SourceShelf = sourceShelf
            };
            List<CustomerStop> customerStops = shiftManagementRepository.GetAddressesByRouteShelf(preShiftLoadBalancing).DataTableToList<CustomerStop>();
            return customerStops;
        }

        public List<CustomerStop> GetActualsCustomerStops(string terminalName, string sourceRoute, string sourceShelf)
        {
            var preShiftLoadBalancing = new PreShiftLoadBalancing()
            {
                TerminalName = terminalName,
                SourceRoute = sourceRoute,
                SourceShelf = sourceShelf
            };
            List<CustomerStop> customerStops = shiftManagementRepository.GetActualsAddressesByRouteShelf(preShiftLoadBalancing).DataTableToList<CustomerStop>();
            return customerStops;
        }

        public List<CustomerStop> GetCustomerStopsFromOtherShelves(PreShiftLoadBalancing preShiftLoadBalancing)
        {
            List<CustomerStop> _customerStops = shiftManagementRepository.GetAddressesFromOtherShelves(preShiftLoadBalancing).DataTableToList<CustomerStop>();
            return _customerStops;
        }

        public bool TransferLoad(PreShiftLoadBalancing preShiftLoadBalancing)
        {
            bool isUpdated = shiftManagementRepository.TransferLoad(preShiftLoadBalancing);
            return isUpdated;
        }

        public bool UndoLoadBalanceMoves(string terminalName, string userName)
        {
            bool isRestored = shiftManagementRepository.UndoLoadBalanceMoves(terminalName, userName);
            return isRestored;
        }

        public List<ShiftStatistics> GetShiftStatistics(string terminalName)
        {
            List<ShiftStatistics> shiftStats = shiftManagementRepository.GetShiftStatisticsDetails(terminalName).DataTableToList<ShiftStatistics>(); 
            return shiftStats;
        }


        public List<MoveHistory> GetMoveHistory(string terminalName, DateTime date)
        {
            List<MoveHistory> moveHistory = shiftManagementRepository.GetMoveHistory(terminalName, date).DataTableToList<MoveHistory>();
            return moveHistory;
        }

        public bool IsShiftStarted(string terminalName)
        {
            ShiftStatus shiftStatus = shiftManagementRepository.IsShiftStarted(terminalName);
            return shiftStatus.IsShiftStarted;            
        }

        public ShiftStatus GetShiftStatus(string terminalName)
        {
            return  shiftManagementRepository.IsShiftStarted(terminalName);            
        }


        public string GetWorkingDay(string terminal)
        {
            return shiftManagementRepository.GetWorkingDay(terminal);
        }

        public string GetAutoStartTime(string terminal)
        {
            return shiftManagementRepository.GetAutoStartTime(terminal);
        }


        public bool SetAutoStartTime(string terminal, string autoStartTime)
        {
            return shiftManagementRepository.SetAutoStartTime(terminal, autoStartTime);
        }

        public bool ApplyPrevMove(string terminal, DateTime date, string user)
        {
            return shiftManagementRepository.ApplyPrevMove(terminal, date, user);
        }
        
        #endregion


        #region Private Methods
        private dynamic GenerateVehicleVolumeReportSummary(string terminalName, bool frozen)
        {
            int mrStops = 0, mrPieces = 0, projectedStops = 0, projectedPieces = 0, actualStops = 0, actualPieces = 0;
            DateTime shiftDate = DateTime.Now;
            List<VehicleVolumeReport> vehicleVolumeReports = null;
            dynamic vehicleVolumeReportSummary = null;
            var dataTableCollection = shiftManagementRepository.GetVehicleVolumeReport(terminalName, frozen, out shiftDate);

            if (dataTableCollection != null && dataTableCollection.Count >= 3)
            {
                var totalsTable =  dataTableCollection[2];
                var mrDataRowList = dataTableCollection[1].AsEnumerable().ToList();
                var totalsDataRow = totalsTable.AsEnumerable().ToList();
                vehicleVolumeReports = dataTableCollection[0].DataTableToList<VehicleVolumeReport>(false, true);

                if (vehicleVolumeReports != null && mrDataRowList != null && vehicleVolumeReports.Count > 0 && (vehicleVolumeReports.Count / 4) == mrDataRowList.Count)
                {
                    DataRow dataRow = null;
                    vehicleVolumeReports.ForEach((item) =>
                    {
                        int mrVal = 0;

                        if (dataRow == null || (dataRow[Constants.RouteTableColumnName] != null && !String.Equals(dataRow[Constants.RouteTableColumnName].ToString(), item.Route, StringComparison.OrdinalIgnoreCase)))
                        {
                            dataRow = mrDataRowList.FirstOrDefault(item2 => item2[Constants.RouteTableColumnName] != null &&
                                item2[Constants.RouteTableColumnName].ToString().Equals(item.Route, StringComparison.OrdinalIgnoreCase));
                        }
                        if (dataRow != null)
                        {
                            if (String.Equals(item.Of, Constants.PiecesDbValue, StringComparison.OrdinalIgnoreCase) && dataRow[Constants.MinPiecesTableColumnName] != null &&
                                int.TryParse(dataRow[Constants.MinPiecesTableColumnName].ToString(), out mrVal))
                            {
                                item.MR = mrVal;
                            }
                            else if (String.Equals(item.Of, Constants.StopsDbValue, StringComparison.OrdinalIgnoreCase) && dataRow[Constants.MinStopsTableColumnName] != null &&
                                int.TryParse(dataRow[Constants.MinStopsTableColumnName].ToString(), out mrVal))
                            {
                                item.MR = mrVal;
                            }
                        }
                    });

                    if (totalsDataRow.Count >= 1 && totalsTable.Columns.Contains(Constants.ProjectedStopsTableColumnName) && totalsTable.Columns.Contains(Constants.ProjectedPiecesTableColumnName) &&
                        totalsTable.Columns.Contains(Constants.ActualStopsTableColumnName) && totalsTable.Columns.Contains(Constants.ActualPiecesTableColumnName) &&
                        totalsTable.Columns.Contains(Constants.MinStopsTableColumnName) && totalsTable.Columns.Contains(Constants.MinPiecesTableColumnName))
                    {
                        int.TryParse(totalsDataRow[0][Constants.MinStopsTableColumnName].ToString(), out mrStops);
                        int.TryParse(totalsDataRow[0][Constants.MinPiecesTableColumnName].ToString(), out mrPieces);
                        int.TryParse(totalsDataRow[0][Constants.ProjectedStopsTableColumnName].ToString(), out projectedStops);
                        int.TryParse(totalsDataRow[0][Constants.ProjectedPiecesTableColumnName].ToString(), out projectedPieces);
                        int.TryParse(totalsDataRow[0][Constants.ActualStopsTableColumnName].ToString(), out actualStops);
                        int.TryParse(totalsDataRow[0][Constants.ActualPiecesTableColumnName].ToString(), out actualPieces);
                    }
                }
                vehicleVolumeReportSummary = new { VehicleVolumeReports = vehicleVolumeReports, ShiftDate = shiftDate,
                                                   MRStops = mrStops, MRPieces = mrPieces,
                                                   ProjectedStops = projectedStops, ProjectedPieces = projectedPieces,
                                                   ActualStops = actualStops, ActualPieces = actualPieces
                };
            }
            return vehicleVolumeReportSummary;
        }
        #endregion
    }
}