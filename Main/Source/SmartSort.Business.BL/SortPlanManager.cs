﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using SmartSort.DataAccessLayer.SortPlanManagement;


namespace SmartSort.Business.Components
{
    public class SortPlanManager
    {
        #region Private Variables
        private readonly SortPlanRepository sortPlanRepository = new SortPlanRepository(Utility.GetDatabaseConnectionName());
        private readonly SortPlanManagementRepository sortPlanManagementRepository = new SortPlanManagementRepository(Utility.GetDatabaseConnectionName());
        private TerminalConfigurationManager terminalConfigurationManager = new TerminalConfigurationManager();
        #endregion

        #region Public Methods

        /// <summary>
        /// Get sort plans for terminal,month and year
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="startDate">Month</param>
        /// <param name="endDate">Year</param>
        /// <returns>List of sort plan entities</returns>
        public List<RouteSortPlan> GetSortPlanForCalendar(string terminal, string startDate, string endDate)
        {
            var sortPlanByMonthList = sortPlanRepository.GetSortPlanForCalendar(terminal, startDate, endDate);
            return sortPlanByMonthList ?? new List<RouteSortPlan>();
        }

        /// <summary>
        /// Get active sort plans for terminal,date
        /// </summary>
        /// <param name="terminal">Terminal</param>
        /// <param name="date">Date</param>
        /// <param name="activeSortPlans">Active Sort Plans</param>
        /// <returns>List of active sort plan entities</returns>
        public List<RouteSortPlan> GetActiveSortPlansForTerminal(string terminal, string date, out List<RouteSortPlan> activeSortPlans)
        {
            var sortPlanByMonthList = sortPlanRepository.GetActiveSortPlansForTerminal(terminal, date, out activeSortPlans);
            return sortPlanByMonthList;
        }

        /// <summary>
        /// Update sort plan for terminal and date
        /// </summary>
        /// <param name="sortPlan">Sort Plan Entity</param>
        /// <returns>True or False</returns>
        public bool UpdateSortPlanForDate(RouteSortPlan sortPlan)
        {
            return sortPlanRepository.UpdateSortPlanForDate(sortPlan);
        }

        /// <summary>
        /// Update sort plans for terminal and recurrence
        /// </summary>
        /// <param name="sortPlan">Sort Plan Entity</param>
        /// <returns>True or False</returns>
        public bool UpdateSortPlanForRecurrence(RouteSortPlan sortPlan)
        {
            return sortPlanRepository.UpdateSortPlanForRecurrence(sortPlan);
        }

        /// <summary>
        /// GetSortPlanForRoutePlan
        /// </summary>
        /// <param name="terminal">terminal</param>
        /// <param name="sortPlanId">sortPlanId</param>
        /// <returns>SortPlanManagement</returns>
        public List<RouteSortPlan> GetSortPlanForRoutePlan(string terminal, int routePlanCodeId)
        {
            List<RouteSortPlan> sortPlan = null;
            if (!String.IsNullOrEmpty(terminal))
            {
                DataTable dataTable = sortPlanManagementRepository.GetSortPlanForRoutePlan(terminal, routePlanCodeId);
                if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
                {

                    sortPlan = dataTable.DataTableToList<RouteSortPlan>();
                }
            }
            return sortPlan;
        }

        /// <summary>
        /// GetActiveRoutePlan
        /// </summary>
        /// <param name="terminal">terminal</param>
        /// <returnsList<SortPlanManagement> ></returns>
        public List<RouteSortPlan> GetActiveRoutePlan(string terminal)
        {
            List<RouteSortPlan> sortPlan = null;
            if (!String.IsNullOrEmpty(terminal))
            {
                DataTable dataTable = sortPlanManagementRepository.GetActiveRoutePlan(terminal);
                if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
                {

                    sortPlan = dataTable.DataTableToList<RouteSortPlan>();
                }
            }
            return sortPlan;
        }

        public SortPlanManagementItem GetSortPlanManagementItem(string terminal, int sortPlanId)
        {
            DataTableCollection dataTableCollection = sortPlanManagementRepository.GetSortPlanInfo(terminal, sortPlanId);
            List<TerminalConfiguration> pudroList = terminalConfigurationManager.GetPudroInfo(terminal);
            SortPlanManagementItem sortPlanManagementItem = null;

            if (dataTableCollection != null && dataTableCollection.Count >= 3)
            {
                sortPlanManagementItem = dataTableCollection[2].DataTableToEntity<SortPlanManagementItem>();

                if (sortPlanManagementItem != null)
                {
                    sortPlanManagementItem.Routes = dataTableCollection[0].Columns.Contains(Constants.AllRoutesTableColumnName) ?
                        dataTableCollection[0].AsEnumerable().Select(item => item[Constants.AllRoutesTableColumnName].ToString()).ToList() :
                        new List<string>();

                    sortPlanManagementItem.UnassignedRoutes = dataTableCollection[1].Columns.Contains(Constants.UnassignedRoutesTableColumnName) ?
                        dataTableCollection[1].AsEnumerable().Select(item => item[Constants.UnassignedRoutesTableColumnName].ToString()).ToList() :
                        new List<string>();

                    sortPlanManagementItem.AssignedRoutes = sortPlanManagementItem.Routes.Except(sortPlanManagementItem.UnassignedRoutes).ToList();

                    foreach (TerminalConfiguration pudroItem in pudroList)
                    {
                        PudroConfigurationItem pudroConfigItem = new PudroConfigurationItem(pudroItem);
                        int lineNumber = 0;

                        dataTableCollection[3].AsEnumerable().Where(item => item[Constants.LineNumberTableColumnName] != null && item[Constants.TermConfigIdTableColumnName] != null &&
                            item[Constants.RoutePositionTableColumnName] != null && int.TryParse(item[Constants.LineNumberTableColumnName].ToString(), out lineNumber) &&
                            lineNumber == pudroItem.LineNumber).ToList().ForEach(
                                    (item) =>
                                    {
                                        int routePosition = 0;
                                        int termConfigurationId = 0;

                                        if (int.TryParse(item[Constants.RoutePositionTableColumnName].ToString(), out routePosition) &&
                                            int.TryParse(item[Constants.TermConfigIdTableColumnName].ToString(), out termConfigurationId))
                                        {
                                            string routeName = item[Constants.RouteNameTableColumnName] as string;
                                            string truckNumber = item[Constants.TruckNumberTableColumnName] as string;
                                            string beltSide = item[Constants.BeltSideTableColumnName] as string;
                                            bool isActive = String.Equals(Constants.YesShortDbValue, item[Constants.ActiveTableColumnName]);

                                            RouteDetails vehicleRouteDetails = new RouteDetails()
                                            {
                                                TerminalConfigurationId = termConfigurationId,
                                                RouteName = routeName,
                                                BeltSide = beltSide,
                                                RoutePosition = routePosition,
                                                VehicleName = truckNumber,
                                                IsActive = isActive,
                                                IsPlaceholder = false
                                            };

                                            if (String.Equals(beltSide, Constants.LeftBeltSideDbValue, StringComparison.OrdinalIgnoreCase) && !pudroConfigItem.LeftRoutes.ContainsKey(routePosition) &&
                                                routePosition <= pudroConfigItem.LeftRouteCount)
                                            {
                                                pudroConfigItem.LeftRoutes.Add(routePosition, vehicleRouteDetails);
                                            }
                                            else if (String.Equals(beltSide, Constants.CenterBeltSideDbValue, StringComparison.OrdinalIgnoreCase) && !pudroConfigItem.CenterRoutes.ContainsKey(routePosition) &&
                                                routePosition <= pudroConfigItem.CenterRouteCount)
                                            {
                                                pudroConfigItem.CenterRoutes.Add(routePosition, vehicleRouteDetails);
                                            }
                                            else if (String.Equals(beltSide, Constants.RightBeltSideDbValue, StringComparison.OrdinalIgnoreCase) && !pudroConfigItem.RightRoutes.ContainsKey(routePosition) &&
                                                routePosition <= pudroConfigItem.RightRouteCount)
                                            {
                                                pudroConfigItem.RightRoutes.Add(routePosition, vehicleRouteDetails);
                                            }
                                        }
                                    }
                            );

                        pudroConfigItem.BuildMissingRoutePositions();
                        sortPlanManagementItem.PudroConfigrationItems.Add(pudroConfigItem);
                    }
                }
            }

            return sortPlanManagementItem;
        }


        public bool UpdateSortPlanInfo(SortPlanManagementItem sortPlanManagementItem)
        {
            return sortPlanManagementRepository.UpdateSortPlanInfo(sortPlanManagementItem);
        }       

        public bool RemoveSortPlanRecurrence(int sortPlanScheduleId,string userName)
        {
            return sortPlanRepository.RemoveSortPlanRecurrence(sortPlanScheduleId, userName);
        }

        public List<RouteSortPlan> GetSortPlanScheduleDates(RouteSortPlan routeSortPlan)
        {
            return sortPlanRepository.GetSortPlanScheduleDates(routeSortPlan);
        }
        #endregion

        public DateTime GetMaxEndDate()
        {
            return sortPlanRepository.GetMaxEndDate();
        }
    }
}