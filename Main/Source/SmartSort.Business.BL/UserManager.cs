﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using SmartSort.DataAccessLayer.UserManagement;

namespace SmartSort.Business.Components
{
    public class UserManager
    {
        #region Private Variables
        private readonly UserManagementRepository userManagementRepository = new UserManagementRepository(Utility.GetDatabaseConnectionName());
        #endregion

        #region Public Methods
        public UserManagement GetUserManagementDetails(int currentUserId)
        {
            DataTable dataTable = userManagementRepository.GetUserManagementDetails(currentUserId);
            UserManagement userManagement = null;

            if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
            {
                userManagement = (UserManagement)XmlParser.ConvertXmlToBusinessObject<UserManagement>(dataTable.Rows[0][0].ToString());
            }

            return userManagement;
        }

        public List<string> GetAssignableRoles(int currentUserId)
        {
            DataTable dataTable = userManagementRepository.GetAssignableRoles(currentUserId);
            List<string> roles = null;

            if (dataTable != null)
            {
                roles = dataTable.AsEnumerable().Select(item => item.Field<string>("ChildRole")).ToList();
            }

            return roles;
        }

        public List<string> GetUserTerminals(int currentUserId)
        {
            DataTable dataTable = userManagementRepository.GetUserTerminals(currentUserId);
            List<string> roles = null;

            if (dataTable != null)
            {
                roles = dataTable.AsEnumerable().Select(item => item.Field<string>("Terminal")).ToList();
            }

            return roles;
        }

        public bool CreateUser(UserProfileDetails userDetails, out int newUserId)
        {
            return userManagementRepository.CreateUser(userDetails, out newUserId);
        }

        public bool UpdateUser(UserProfileDetails userDetails)
        {
            return userManagementRepository.UpdateUserProfile(userDetails);
        }

        public void UpdateSAPAccount(UserProfileDetails userDetails)
        {
            userManagementRepository.UpdateSAPAccount(userDetails);
        }

        public bool DeleteUser(string currentUserName, int userId)
        {
            return userManagementRepository.DisableUser(currentUserName, userId);
        }
        #endregion
    }
}