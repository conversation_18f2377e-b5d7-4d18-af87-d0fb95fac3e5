﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Diagnostics;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using System.Data;
using SmartSort.DataAccessLayer.TerminalConfiguration;

namespace SmartSort.Business.Components
{
    public class TerminalConfigurationManager
    {
        #region Global Variables
        private DataTable datatable;
        private bool saveStatus;
        #endregion Global Variables
        #region Private Variables

        private readonly TerminalConfigurationRepository terminalConfigurationRepository = new TerminalConfigurationRepository(Utility.GetDatabaseConnectionName());
        private readonly SortingLineRepository sortingLineRepository = new SortingLineRepository(Utility.GetDatabaseConnectionName());

        #endregion Private Variables


        #region PublicMethods
        /// <summary>
        /// Get the Pudro information list
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns></returns>
        public List<TerminalConfiguration> GetPudroInfo(String terminal)
        {
            List<TerminalConfiguration> terminalConfigurationList = null;
            datatable = terminalConfigurationRepository.GetPudroInfo(terminal);
            if (datatable != null)
            {
                terminalConfigurationList = datatable.DataTableToList<TerminalConfiguration>().OrderBy(item => item.LineNumber).ToList();
            }

            return terminalConfigurationList;
        }

        public int GetTotalActiveSides(string terminal)
        {
            return terminalConfigurationRepository.GetTotalActiveSides(terminal);
        }

        /// <summary>
        /// GetLineDetails
        /// </summary>
        /// <param name="terminal">terminal</param>
        /// <param name="lineNumber">lineNumber</param>
        /// <returns>List<TerminalConfiguration></returns>
        public List<TerminalConfiguration> GetLineDetails(String terminal, int lineNumber, out int totalRoutes)
        {
            List<TerminalConfiguration> terminalConfigurationList = null;
            datatable = terminalConfigurationRepository.GetLineDetails(terminal, lineNumber, out totalRoutes);
            if (datatable != null)
            {
                terminalConfigurationList = datatable.DataTableToList<TerminalConfiguration>().OrderBy(item => item.LineNumber).ToList();
            }

            return terminalConfigurationList;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sortingLine">List<SortingLine></param>
        /// <returns>boolean</returns>
        public Boolean UpdateSortingLine(SortingLineDetails sortingLineDetails)
        {

            saveStatus = false;
            if(sortingLineDetails!=null)
            saveStatus = sortingLineRepository.UpdateLineDetails(sortingLineDetails);

            return saveStatus;
        }
        #endregion
    }
}
