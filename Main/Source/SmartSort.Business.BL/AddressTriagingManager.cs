﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Diagnostics;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer.AddressTriaging;

namespace SmartSort.Business.Components
{
    public class AddressTriagingManager
    {
        #region Global Variables

        private DataTable dataTable;
        private DataRow dataRow;
        private bool saveStatus;

        #endregion Global Variables

        #region Private Variables

        private readonly AddressTriagingRepository addressTriagingRepository = new AddressTriagingRepository(Utility.GetDatabaseConnectionName());

        #endregion Private Variables

        #region Public Methods

        /// <summary>
        /// GetAddressTriagerecord Return List of AddressTriaging
        /// </summary>
        /// <param name="addressRequest"></param>
        /// <param name="noOfRecords"></param>
        /// <returns>List of AddressTriaging</returns>
        public List<AddressTriaging> GetAddressTriageRecord(AddressRequest addressRequest, out int noOfRecords)
        {
            List<AddressTriaging> addressTriageList = null;
            noOfRecords = 0;
           
            dataTable = addressTriagingRepository.GetAddressTriageRecord(addressRequest, out noOfRecords);
            if (dataTable != null)
                {
                addressTriageList = dataTable.DataTableToList<AddressTriaging>();
                }
                return addressTriageList;                      
      }

        /// <summary>
        ///
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns></returns>
        public List<AddressCommonDetails> GetAllRoutes(string terminal)
        {
            List<AddressCommonDetails> addressCommonList = null;
            dataTable = addressTriagingRepository.GetAllRoutes(terminal);
            if (dataTable != null)
                {
                addressCommonList = dataTable.DataTableToList<AddressCommonDetails>();
                }
                return addressCommonList;    
        }

        public List<AddressCommonDetails> GetAllShelfForRoute(string terminal, string routes)
        {
            List<AddressCommonDetails> addressCommonList = null;

            dataTable = addressTriagingRepository.GetAllShelfForRoute(terminal, routes);
            if (dataTable != null)
                {
                addressCommonList = dataTable.DataTableToList<AddressCommonDetails>();
                }
                return addressCommonList;           
        }

        /// <summary>
        /// Getting Address based on Id.
        /// </summary>
        /// <param name="triageQueueId"></param>
        public AddressDetails GetAddress(int triageQueueId)
        {
            AddressDetails addressDetails = null;

            dataTable = addressTriagingRepository.GetAddress(triageQueueId);

            if (dataTable != null && dataTable.Rows.Count > 0)
                dataRow = dataTable.Rows[0];

            if (dataRow != null)
                {
                addressDetails = dataTable.DataTableToEntity<AddressDetails>();
                }
            
                return addressDetails;           
        }

        /// <summary>
        /// Get List of Triage pin details
        /// </summary>
        /// <param name="triageQueueId">TriageQueueID</param>
        /// <returns>List of AddressTraigePin</returns>
        public List<AddressTriagePin> GetTriagePinDetails(int triageQueueId)
        {
            List<AddressTriagePin> addressPinDetails = null;

            dataTable = addressTriagingRepository.GetTriagePinDetails(triageQueueId);
            if (dataTable != null)
                {
                addressPinDetails = dataTable.DataTableToList<AddressTriagePin>();
                }

                return addressPinDetails;            
        }

        /// <summary>
        /// Updating Triage Address in Database.
        /// </summary>
        /// <param name="addressDetails"></param>
        /// <param name="status"></param>
        /// <param name="route"></param>
        /// <param name="shelf"></param>
        /// <returns>Boolean update message</returns>
        public void UpdateTriageAddress(AddressDetails addressDetails, out bool status, out bool alreadyResolved, out string route, out string shelf)
        {
            saveStatus = false;
            status = false;
            alreadyResolved = false;
            route = string.Empty;
            shelf = string.Empty;
            if (null != addressDetails)
            {
                addressTriagingRepository.UpdateTriageAddress(addressDetails, out status, out alreadyResolved, out route, out shelf);
            }
        }

        /// <summary>
        /// Update Triage Queue.
        /// </summary>
        /// <param name="triageQueueId"></param>
        /// <param name="userName"></param>
        /// <param name="updateDateTimeOffset"></param>
        /// <returns>IDataReader</returns>
        public bool UpdateTriageLastTouch(int triageQueueId, string userName, DateTimeOffset updateDateTimeOffset)
        {
            saveStatus = false;
            saveStatus = addressTriagingRepository.UpdateTriageLastTouch(triageQueueId, userName, updateDateTimeOffset);
            return saveStatus;
        }


        public bool DeleteAddresses(List<int> triageQueueIds)
        {
            saveStatus = false;
            saveStatus = addressTriagingRepository.DeleteAddresses(triageQueueIds);
            return saveStatus;
        }

        

        /// <summary>
        /// Get Triage Reason Code
        /// </summary>
        /// <returns></returns>
        public List<AddressTriaging> GetTriageReasonCodes()
        {

            List<AddressTriaging> addressTriageList = null;
            //dataTable = addressTriagingRepository.GetTriageReasonCodes();
            //if (dataTable != null)
            //{
            //    addressTriageList = addressTriagingRepository.GetTriageReasonCodes().DataTableToList<AddressTriaging>();
            //}
            addressTriageList = addressTriagingRepository.GetTriageReasonCodes().DataTableToList<AddressTriaging>();
            return addressTriageList;
        }

        public AddressInfoByRoute GetAddressFromRoutePlan(int Id)
        {
            AddressInfoByRoute address = null;
            dataTable = addressTriagingRepository.GetAddressFromRoutePlanById(Id);
            if (dataTable != null)
            {
                address = dataTable.DataTableToEntity<AddressInfoByRoute>();
            }
            return address;
        }


        /// <summary>
        /// Getting Address From RoutePlan based on Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <param name="postalCode"></param>
        /// <returns>List of Address by route</returns>
        public List<AddressInfoByRoute> GetAddressFromRoutePlan(string terminal, string postalCode)
        {
            List<AddressInfoByRoute> addressInfoByRoutes = new List<AddressInfoByRoute>();
            dataTable = addressTriagingRepository.GetAddressFromRoutePlan(terminal, postalCode);
            if (dataTable != null)
            {
                addressInfoByRoutes = dataTable.DataTableToList<AddressInfoByRoute>();
            }
            return addressInfoByRoutes;
        }

        /// <summary>
        /// Getting the Street name based in Terminal
        /// </summary>
        /// <param name="terminal"></param>
        /// <param name="streetName"></param>
        /// <param name="postalCode"></param>
        /// <returns>List of Addresses</returns>
        public List<AddressInfoByRoute> GetStreetNameFromRoutePlan(string terminal, string streetName, string postalCode)
        {
            List<AddressInfoByRoute> addressCommonDetails = new List<AddressInfoByRoute>();
            dataTable = addressTriagingRepository.GetStreetNameFromRoutePlan(terminal, streetName, postalCode);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressInfoByRoute>();
            }
            return addressCommonDetails;
        }

        public List<AddressInfoByRoute> GetCustomerNameFromRoutePlan(string terminal, string customerName, string postalCode)
        {
            List<AddressInfoByRoute> addressCommonDetails = new List<AddressInfoByRoute>();
            dataTable = addressTriagingRepository.GetCustomerNameFromRoutePlan(terminal, customerName, postalCode);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressInfoByRoute>();
            }
            return addressCommonDetails;
        }


        public List<AddressInfoByRoute> GetUnitNumberFromRoutePlan(string terminal, string unitNumber, string postalCode)
        {
            List<AddressInfoByRoute> addressCommonDetails = new List<AddressInfoByRoute>();
            dataTable = addressTriagingRepository.GetUnitNumberFromRoutePlan(terminal, unitNumber, postalCode);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressInfoByRoute>();
            }
            return addressCommonDetails;
        }


        


        public List<AddressCommonDetails> GetCustomerNameFromRoutePlan(string terminal, string postalCode)
        {
            List<AddressCommonDetails> addressCommonDetails = new List<AddressCommonDetails>();
            dataTable = addressTriagingRepository.GetCustomerNameFromRoutePlan(terminal, postalCode);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
            }
            return addressCommonDetails;
        }

        /// <summary>
        /// Getting All Routes and Shelf's based on Terminal
        /// </summary>
        /// <param name="terminal"></param>
        public List<AddressCommonDetails> GetAllRouteAndShelfFromRoutePlan(string terminal)
        {
            List<AddressCommonDetails> addressCommonDetails = new List<AddressCommonDetails>();
            dataTable = addressTriagingRepository.GetAllRouteAndShelfFromRoutePlan(terminal);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
            }
            return addressCommonDetails;
        }

        /// <summary>
        /// Updating Route and Shelf for a Terminal.
        /// </summary>
        /// <param name="addressDetails"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public bool UpdateRouteAndShelf(AddressDetails addressDetails, out string status)
        {
            return addressTriagingRepository.UpdateRouteAndShelf(addressDetails, out status);
        }

        /// <summary>
        /// Getting Provinces List for a Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>List of Street Provinces</returns>
        public List<AddressCommonDetails> GetProvincesList(string terminal)
        {
            List<AddressCommonDetails> addressCommonDetails = new List<AddressCommonDetails>();
            dataTable = addressTriagingRepository.GetProvincesList(terminal);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
                if (addressCommonDetails != null)
                {
                    addressCommonDetails = addressCommonDetails.Where(item => !String.IsNullOrWhiteSpace(item.ProvinceCode)).ToList();
                }
            }
            return addressCommonDetails;
        }

        /// <summary>
        /// Getting Street Suffix List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>List of Street Suffix</returns>
        public List<AddressCommonDetails> GetStreetSuffixList(string terminal)
        {
            List<AddressCommonDetails> addressCommonDetails = null;
            dataTable = addressTriagingRepository.GetStreetSuffixList(terminal);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
                if (addressCommonDetails != null)
                {
                    addressCommonDetails = addressCommonDetails.Where(item => !String.IsNullOrWhiteSpace(item.ToStreetNumSuf)).OrderBy(item => item.ToStreetNumSuf).ToList();
                }
            }
            return addressCommonDetails;
        }

        /// <summary>
        /// Getting Street Direction List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>List of Street Directions</returns>
        public List<AddressCommonDetails> GetStreetDirectionList(string terminal)
        {
            List<AddressCommonDetails> addressCommonDetails = null;
            dataTable = addressTriagingRepository.GetStreetDirectionList(terminal);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
                if (addressCommonDetails != null)
                {
                    addressCommonDetails = addressCommonDetails.Where(item => !String.IsNullOrWhiteSpace(item.StreetDirection)).OrderBy(item => item.StreetDirection).ToList();
                }
            }
            return addressCommonDetails;
        }

        /// <summary>
        /// Getting Street Types List for Terminal.
        /// </summary>
        /// <param name="terminal"></param>
        /// <returns>List of StreetTypes</returns>
        public List<AddressCommonDetails> GetStreetTypesList(string terminal)
        {
            List<AddressCommonDetails> addressCommonDetails = null;
            dataTable = addressTriagingRepository.GetStreetTypesList(terminal);
            if (dataTable != null)
            {
                addressCommonDetails = dataTable.DataTableToList<AddressCommonDetails>();
                if (addressCommonDetails != null)
                {
                    addressCommonDetails = addressCommonDetails.Where(item => !String.IsNullOrWhiteSpace(item.StreetType)).OrderBy(item => item.StreetType).ToList();
                }
            }
            return addressCommonDetails;
        }

        #endregion Public Methods
    }
}