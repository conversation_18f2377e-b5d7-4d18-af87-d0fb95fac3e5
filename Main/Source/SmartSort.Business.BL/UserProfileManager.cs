﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using SmartSort.DataAccessLayer.UserProfile;

namespace SmartSort.Business.Components
{
    public class UserProfileManager
    {
        #region Private Variables

        private readonly UserProfileRepository userProfileRepository = new UserProfileRepository(Utility.GetDatabaseConnectionName());
        private readonly LoginRepository loginRepository = new LoginRepository(Utility.GetDatabaseConnectionName());
        private DataTable dataTable;

        #endregion Private Variables

        #region UserProfile
        /// <summary>
        /// This method will return the profile information 
        /// for the looged in user 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns>user profile information</returns>
        
        public UserProfileDetails GetProfileInfo(int userId)
        {
            UserProfileDetails userProfile = new UserProfileDetails();
            DataTable dtUserProfile = userProfileRepository.GetProfileInfo(userId);
            if (null != dtUserProfile && null != dtUserProfile.Rows[0])
            {
                userProfile = (UserProfileDetails)XmlParser.ConvertXmlToBusinessObject<UserProfileDetails>(Convert.ToString(dtUserProfile.Rows[0][0]));
            }
            return userProfile;
        }

        /// <summary>
        /// This method will update the logged in user profile
        /// </summary>
        /// <param name="userDetails"></param>
        /// <returns>bool</returns>
        public void SetProfileInfo(UserProfileDetails userDetails, out bool status)
        {
            userProfileRepository.SetProfileInfo(userDetails, out status);  
        }

        #endregion UserProfile


        #region Login
        public string GetTerminalOffset(string terminal)
        {
            string offset = String.Empty;
            DataTable dataTable = userProfileRepository.GetOffsetByTerminal(terminal);
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                offset = dataTable.Rows[0]["TimeZoneOffset"].ToString();
            }

            return offset;
        }

        /// <summary>
        ///This method will fetch all the Smart
        ///sort terminal with status Smart Sort ready
        /// </summary>
        /// <returns>list of terminal </returns>
        public List<string> GetTerminalList
        {
            get
            {
                dataTable = loginRepository.TerminalList;
                List<string> list = null;
                if (dataTable != null)
                {
                    list = dataTable.AsEnumerable()
                                  .Select(r => (r.Field<string>("Terminal") + "-" + r.Field<string>("city")))
                                  .ToList();
                }
                return list;
            }
        }

        ///  <summary>
        /// Return the Logged in User Role 
        ///  </summary>
        /// <param name="loggedInUserId"></param>
        /// <returns>UserRole</returns>
        public string GetUserRole(int loggedInUserId)
        {
            dataTable = loginRepository.GetUserRole(loggedInUserId);
            return dataTable != null ? dataTable.Rows[0][0].ToString() : string.Empty;
        }

        ///  <summary>
        /// This method will validate the selected terminal 
        /// with the terminal assigned to the user
        ///  </summary>
        /// <param name="userId"></param>
        /// <param name="selectedTerminal"></param>
        /// <param name="message"></param>
        /// <returns>message</returns>
        public void ValidateTerminal(string userId, string selectedTerminal, out string message)
        {
            dataTable = loginRepository.ValidateTerminal(userId, selectedTerminal, out message);
        }

        /// <summary>
        /// This Method connect to DAl to get the LoggedIn
        /// user information
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        public UserProfileDetails GetUserInformation(string userName)
        {
            string status;
            UserProfileDetails userProfileNew = new UserProfileDetails();
            DataTable dtUserProfile = loginRepository.GetUserInformation(userName, out status);
            if (dtUserProfile != null)
            {
                userProfileNew = (UserProfileDetails)XmlParser.ConvertXmlToBusinessObject<UserProfileDetails>(dtUserProfile.Rows[0][0].ToString());
            }
            return userProfileNew;
        }

        public UserProfileDetails GetProfileInfoBySAP(string SAPUserName)
        {
            UserProfileDetails userProfile = new UserProfileDetails();
            DataTable dtUserProfile = userProfileRepository.GetProfileInfobySAP(SAPUserName);
            if (null != dtUserProfile && null != dtUserProfile.Rows[0])
            {
                userProfile = (UserProfileDetails)XmlParser.ConvertXmlToBusinessObject<UserProfileDetails>(Convert.ToString(dtUserProfile.Rows[0][0]));
            }
            return userProfile;
        }

        #endregion Login
    }
}