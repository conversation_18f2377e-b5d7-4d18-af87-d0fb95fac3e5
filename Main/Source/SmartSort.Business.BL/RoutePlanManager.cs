﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using SmartSort.DataAccessLayer.RoutePlan;



namespace SmartSort.Business.Components
{
    public class RoutePlanManager
    {
        private readonly RoutePlanRepository routePlanRepository = new RoutePlanRepository(Utility.GetDatabaseConnectionName());

        public List<RoutePlanShort> GetRoutePlans(string terminal)
        {
            return routePlanRepository.GetRoutePlans(terminal);
        }
    }
}
