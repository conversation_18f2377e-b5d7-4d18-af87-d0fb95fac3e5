﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;
using SmartSort.DataAccessLayer.GlobalConfiguration;

namespace SmartSort.Business.Components
{
    public class GlobalConfigurationManager
    {
        #region Private Variables

        private readonly GlobalConfigurationRepository repository = new GlobalConfigurationRepository(Utility.GetDatabaseConnectionName());                

        #endregion Private Variables

      
        public GlobalConfiguration GetGlobalConfig()
        {
            DataTable dataTable = repository.GetGlobalConfiguration();
            string pager = string.Empty;
            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                pager = dataTable.Rows[0]["ParamValue"].ToString();
            }

            GlobalConfiguration config = new GlobalConfiguration();
            config.pager = pager;
            
            return config;
        }

        public bool Update(GlobalConfiguration config)
        {
            return repository.Update(config);            
        }

      
    }
}