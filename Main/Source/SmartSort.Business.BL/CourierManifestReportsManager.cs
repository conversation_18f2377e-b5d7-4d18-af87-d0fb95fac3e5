﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using Purolator.SmartSort.CourierManifestRendering;
using SmartSort.Business.Entities;
using SmartSort.Common;
using SmartSort.DataAccessLayer;


namespace SmartSort.Business.Components
{
    public class CourierManifestReportsManager
    {
        #region Private Variables
        private readonly DataAccessLayer.CourierManifest.CourierManifestRepository courierManifestRepository = new DataAccessLayer.CourierManifest.CourierManifestRepository(Utility.GetDatabaseConnectionName());
        #endregion

        #region Public Methods
        public List<CourierManifestSortPlanRoutesStatus> GetPudroAndRoutes(string terminalName, DateTime date)
        {
            List<CourierManifestSortPlanRoutesStatus> courierManifestSortPlanRoutesStatusList = courierManifestRepository.GetPudroAndRoutes(terminalName, date).
                DataTableToList<CourierManifestSortPlanRoutesStatus>();
            return courierManifestSortPlanRoutesStatusList;
        }

        public bool CloseRoutes(string deviceId, string terminalName, DateTime dateTime, DateTimeOffset dateTimeOffset, string routes)
        {
            bool isSuccess = courierManifestRepository.CloseRoutes(deviceId, terminalName, routes, dateTime, dateTimeOffset);
            return isSuccess;
        }

        public IRenderer GenerateCourierManifestReport(string reportLogoPath, string userName, string terminalName, DateTime dateTime, DateTimeOffset dateTimeOffset, string routes)
        {
            CourierManifestRenderer courierManifestRenderer = null;
            List<CourierManifest> courierManifests = new List<CourierManifest>();
            string[] routeEntries = !String.IsNullOrWhiteSpace(routes) ? routes.Split(',') : null;

            foreach (var route in routeEntries)
            {
                //DateTime dt = DateTime.Now.AddHours(4);
                var courierManifestDataTableCollection = courierManifestRepository.GetCourierManifestReport(terminalName, dateTime, new DateTimeOffset(DateTime.Now), route, userName);

                //string s = "Fri Nov 01 2013 00:00:00 GMT+0400";
                //DateTime dt = DateTime.ParseExact(s, "ddd MMM dd yyyy HH:mm:ss 'GMT'K", CultureInfo.InvariantCulture);

                if (courierManifestDataTableCollection != null && courierManifestDataTableCollection.Count >= 3 && courierManifestDataTableCollection[1] != null &&
                    courierManifestDataTableCollection[1].Rows.Count > 0 && courierManifestDataTableCollection[1].Columns.Count >= 3)
                {
                    int totalVehicleStops = 0, totalCustomerStops = 0, totalPieces = 0;
                    CourierManifest courierManifest = new CourierManifest()
                    {
                        ManifestSummary = new CourierManifestSummary()
                    };

                    int.TryParse(courierManifestDataTableCollection[1].Rows[0][Constants.TotalVehicleStopsTableColumnName].ToString(), out totalVehicleStops);
                    int.TryParse(courierManifestDataTableCollection[1].Rows[0][Constants.TotalCustomerStopsTableColumnName].ToString(), out totalCustomerStops);
                    int.TryParse(courierManifestDataTableCollection[1].Rows[0][Constants.TotalPiecesTableColumnName].ToString(), out totalPieces);

                    courierManifest.ManifestItems = courierManifestDataTableCollection[0].DataTableToList<CourierManifestItem>();
                    courierManifest.ManifestSummary.TotalVehicleStops = totalVehicleStops;
                    courierManifest.ManifestSummary.TotalCustomerStops = totalCustomerStops;
                    courierManifest.ManifestSummary.TotalPieces = totalPieces;
                    courierManifest.ManifestSummary.SummaryItems = courierManifestDataTableCollection[2].DataTableToList<CourierManifestSummaryItem>();
                    courierManifest.ManifestSummary.Route = route;
                    courierManifest.ManifestSummary.TerminalName = terminalName;
                    courierManifest.ManifestSummary.ManifestedDate = dateTime;
                    courierManifest.ManifestSummary.CreationDate = DateTime.Now;
                    
                    courierManifests.Add(courierManifest);
                }
            }

            courierManifestRenderer = new CourierManifestRenderer(reportLogoPath, courierManifests);
            return courierManifestRenderer;
        }
        #endregion
    }
}