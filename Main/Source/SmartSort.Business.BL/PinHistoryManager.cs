﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Purolator.SmartSort.PinHistory;
using SmartSort.Business.Entities.PinHistory;
using SmartSort.DataAccessLayer.PinHistory;
using SmartSort.Common;
using SmartSort.DataAccessLayer;

namespace SmartSort.Business.Components
{
    public class PinHistoryManager
    {
        private readonly PinHistoryRepository pinHistoryRepository = new PinHistoryRepository(Utility.GetDatabaseConnectionName());

        public IRenderer GeneratePinHistory(List<string> pins)
        {
            PinHistoryList pinHistoryList = pinHistoryRepository.GetPinHistory(pins);
            PinHistoryGenerator gen = new PinHistoryGenerator();
            PinHistoryRenderer renderer = new PinHistoryRenderer(gen, pinHistoryList);
            return renderer;            
        }

    }
}
