﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5B8720FC-A239-47D7-BE7E-F8B0832C156F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartSort.Business.Components</RootNamespace>
    <AssemblyName>SmartSort.Business.Components</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>..\SmartSortRuleSet.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="GlobalConfigurationManager.cs" />
    <Compile Include="CourierManifestReportsManager.cs" />
    <Compile Include="AddressTriagingManager.cs" />
    <Compile Include="GlobalSuppressions.cs" />
    <Compile Include="PinHistoryManager.cs" />
    <Compile Include="RoutePlanManager.cs" />
    <Compile Include="ShiftManagementManager.cs" />
    <Compile Include="SortPlanManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TerminalConfigurationManager.cs" />
    <Compile Include="UserManager.cs" />
    <Compile Include="UserProfileManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Purolator.SmartSort.CourierManifestPdf\Purolator.SmartSort.CourierManifestRendering.csproj">
      <Project>{44f1040f-b764-4b9f-acdb-1b3303bcef1b}</Project>
      <Name>Purolator.SmartSort.CourierManifestRendering</Name>
    </ProjectReference>
    <ProjectReference Include="..\Purolator.SmartSort.PinHistory\Purolator.SmartSort.PinHistory.csproj">
      <Project>{2d713c48-c831-4f41-b87f-46d84b60ebf8}</Project>
      <Name>Purolator.SmartSort.PinHistory</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Business.Entities\SmartSort.Business.Entities.csproj">
      <Project>{5662ba8a-f770-420b-a01c-a388b211f25d}</Project>
      <Name>SmartSort.Business.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.Common\SmartSort.Common.csproj">
      <Project>{4a28b5ff-887c-4a9a-b054-5e88f1119ab3}</Project>
      <Name>SmartSort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\SmartSort.DataAccessLayer\SmartSort.DataAccessLayer.csproj">
      <Project>{29384837-9f0a-4b52-b9ca-a6df63bf817e}</Project>
      <Name>SmartSort.DataAccessLayer</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>