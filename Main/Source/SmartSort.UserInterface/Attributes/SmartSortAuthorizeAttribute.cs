﻿using System;
using System.Net;
using System.Collections.Generic;
using System.Web;
using System.Web.Mvc;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Routing;
using SmartSort.Common;
using SmartSort.UserInterface.Common;
using System.Configuration;
using SmartSort.UserInterface.Controllers;
using Purolator.SmartSort.Clients.SSO;
using SmartSort.Business.Components;
using SmartSort.Business.Entities;
using SmartSort.UserInterface.Helpers;
using System.Globalization;

namespace SmartSort.UserInterface.Attributes
{
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public sealed class SmartSortAuthorizeAttribute : AuthorizeAttribute
    {

        #region Private Methods
        private bool IsValidateStickySession()
        {
            bool isPerformValidation = false;
            var configEntry = ConfigurationManager.AppSettings[Constants.ValidateStickySessionConfigKey];
            return configEntry != null && bool.TryParse(configEntry, out isPerformValidation) && isPerformValidation;
        }

        private bool IsStickySessionValid(AuthorizationContext filterContext)
        {
            string sourceServerHostName = filterContext.HttpContext.Request.Cookies[Constants.ValidateStickySessionCookieKey] != null ?
                filterContext.HttpContext.Request.Cookies[Constants.ValidateStickySessionCookieKey].Value : String.Empty;
            string currentServerHostName = Dns.GetHostName();
            bool isValid = !(!String.IsNullOrWhiteSpace(sourceServerHostName) && !String.IsNullOrWhiteSpace(currentServerHostName) && !String.Equals(sourceServerHostName, currentServerHostName, StringComparison.OrdinalIgnoreCase));
            return isValid;
        }

        private void SetServerCookie(AuthorizationContext filterContext)
        {
            if (filterContext != null)
            {
                string currentServerHostName = Dns.GetHostName();
                filterContext.HttpContext.Response.Cookies.Add(new HttpCookie(Constants.ValidateStickySessionCookieKey, currentServerHostName));
            }
        }
        #endregion

        /// <summary>
        /// Checking for the authorization of user
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnAuthorization(AuthorizationContext filterContext)
        {
            bool needAuthorization = false;

            #region  SAP SSO
            // check if user is logged in
            string userdId = SessionManager<string>.GetFromSession(Constants.USER_ID);
            
            if (string.IsNullOrWhiteSpace(userdId))
            {
                // check cookie
                HttpCookie mysapsso2 = filterContext.HttpContext.Request.Cookies["MYSAPSSO2"];
                if (mysapsso2 != null)
                {
                    // call SAP SSO WS
                    SSOClient client = new SSOClient();
                    string sapUsername = client.GetSAPUsername(HttpUtility.UrlDecode(mysapsso2.Value));
                    if (sapUsername != null)
                    {
                        // lookup user
                        try
                        {
                            UserProfileManager userManager = new UserProfileManager();
                            UserProfileDetails userDetails = userManager.GetProfileInfoBySAP(sapUsername);
                            if (userDetails != null && userDetails.UserInfo != null && userDetails.UserInfo.UserId > 0)
                            {
                                var userFunctions = userDetails.Functions.FunctionCode;
                                string strUserFunction = string.Empty;
                                strUserFunction = userFunctions.Aggregate(strUserFunction, (current, function) => current + (function + ","));
                                strUserFunction = strUserFunction.TrimEnd(',');
                                
                                SessionManager<string>.SetInSession(Constants.USER_ID, userDetails.UserInfo.UserId.ToString());
                                SessionManager<string>.SetInSession(Constants.FULL_NAME, userDetails.UserInfo.FirstName.ToString() + " " + userDetails.UserInfo.LastName.ToString());
                                SessionManager<string>.SetInSession(Constants.USER_FUNCTION, strUserFunction);
                                SessionManager<string>.SetInSession(Constants.USER_NAME, sapUsername);

                                // log ther user in
                                if (userDetails.Terminals != null)
                                {
                                    var lstTerminal = userDetails.Terminals.Terminal;
                                    LanguageHelper.InitializeCulture(userDetails.UserInfo.Language);

                                    int terminalCount = lstTerminal.Count;

                                    if (terminalCount == 1)
                                    {
                                        string selectedTerminal = lstTerminal[0];
                                        string formattedTerminal = selectedTerminal.Substring(0, selectedTerminal.IndexOf('-')).TrimEnd();
                                        string offset = userManager.GetTerminalOffset(formattedTerminal);
                                        TimeZoneInfo timeZoneInfo = Utility.GetTimeZoneInfoByOffset(offset);

                                        SessionManager<TimeZoneInfo>.SetInSession(Constants.TERMINAL_TIMEZONEINFO, timeZoneInfo); 
                                        SessionManager<string>.SetInSession(Constants.TERMINAL_NUMBER_CITY, selectedTerminal.TrimEnd());
                                        SessionManager<string>.SetInSession(Constants.TERMINAL_NUMBER, formattedTerminal);
                                    }
                                    else
                                    {
                                        SessionManager<List<string>>.SetInSession(Constants.TERMINALS_TO_SELECT, lstTerminal);
                                        filterContext.Result = new RedirectToRouteResult(
                                        new RouteValueDictionary(
                                             new
                                             {
                                                 controller = Constants.ACCOUNT_CONTROLLER,
                                                 action = "SelectTerminal"
                                             })
                                        );
                                        if (filterContext.ActionDescriptor.ActionName.ToUpper() != Constants.LOGIN && filterContext.ActionDescriptor.ControllerDescriptor.ControllerName.ToUpper() != Constants.ERROR)
                                        {
                                            string redirectURL = filterContext.HttpContext.Request.RawUrl;
                                            if (!redirectURL.Contains("?"))
                                            {
                                                SessionManager<string>.SetInSession(Constants.REDIRECT_URL, redirectURL);
                                            }
                                        }
                                        return;
                                    }
                                }                                                              
                            }
                            else
                            {
                                // set in session and redirect to login
                                SessionManager<string>.SetInSession(Constants.SAP_USER_ID, sapUsername);
                            }
                        }
                        catch (Exception ex)
                        {
                            SmartSortLogger.Error("Error in SSO login", ex);
                        }                       
                    }
                }
            }
            #endregion

            if (filterContext.ActionDescriptor.ControllerDescriptor.ControllerName == Constants.ADDRESS_CONTROLLER
                && filterContext.ActionDescriptor.ActionName.ToUpper() == Constants.Address_Index_Action)
            {
                string redirectURL = filterContext.HttpContext.Request.RawUrl;
                if (!redirectURL.Contains("?"))
                {
                    SessionManager<string>.SetInSession(Constants.REDIRECT_URL, redirectURL);
                }
            }
            if (!((filterContext.ActionDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true)) | (filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(AllowAnonymousAttribute), true))))
            {
                needAuthorization = (filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(SmartSortAuthorizeAttribute), true));
            }
            if (needAuthorization)
            {
                if (!IsRoleToControllerMapped(filterContext))
                {
                    HandleUnauthorizedRequest(filterContext);
                }
                else if (IsValidateStickySession())
                {
                    SetServerCookie(filterContext);
                }
            }
        }

        /// <summary>
        /// Redirecting the unauthorized user to error page.
        /// </summary>
        /// <param name="filterContext"></param>
        protected override void HandleUnauthorizedRequest(AuthorizationContext filterContext)
        {
            if (filterContext != null)
            {
                if (IsValidateStickySession() && !IsStickySessionValid(filterContext))
                {
                    string sourceServer = filterContext.HttpContext.Request.Cookies[Constants.ValidateStickySessionCookieKey].Value;
                    string targetServer = Dns.GetHostName();
                    string url = String.Format("/Account/Login?SourceServer={0}&TargetServer={1}", sourceServer, targetServer);
                    
                    filterContext.HttpContext.Response.Cookies.Remove(Constants.ValidateStickySessionConfigKey);
                    filterContext.Result = new RedirectResult(url);
                }
                else
                {
                    filterContext.Result = new RedirectToRouteResult(
                                new RouteValueDictionary(
                                    new
                                    {
                                        controller = Constants.ACCOUNT_CONTROLLER,
                                        action = "Login"
                                    })
                                );
                }

            }
        }

        /// <summary>
        /// Mapping the user function with controllers.
        /// </summary>
        /// <param name="controllerName"></param>
        /// <returns></returns>
        public bool IsRoleToControllerMapped(AuthorizationContext filterContext)
        {
            bool result = false;
            if (null != filterContext)
            {
                string controllerName = filterContext.ActionDescriptor.ControllerDescriptor.ControllerName;
                string userFunctions = SessionManager<string>.GetFromSession(Constants.USER_FUNCTION);
                string userTerminals = SessionManager<string>.GetFromSession(Constants.TERMINAL_NUMBER);

                Dictionary<string, string> controllerNameDict = new Dictionary<string, string>
            {
                {Constants.ADDRESS_CONTROLLER, "ATR"},
                {Constants.REPORTS_CONTROLLER, "CM"},
                {Constants.SHIFTMANAGEMENT_CONTROLLER, "SHM"},
                {Constants.SORTPLAN_CONTROLLER, "SPC"},
                {Constants.ACCOUNT_CONTROLLER, "UM"},
                {Constants.ERROR_CONTROLLER, "UM"},
                {Constants.HOME_CONTROLLER, "UM"},
                {Constants.USERPROFILE_CONTROLLER, "UM"},
                {Constants.TERMINALCONFIGURATION_CONTROLLER, "TC"},
                {Constants.SORTPLANMANAGEMENT_CONTROLLER, "SPM"},
                {Constants.ROUTEPLANMANAGEMENT_CONTROLLER, "SPM"},
                {Constants.PIN_HISTORY_CONTROLLER, "SHM"},
                {Constants.MOVE_HISTORY_CONTROLLER, "SHM"},
                {Constants.GLOBAL_CONFIGURATION_CONTROLLER, "GC"}
            };

                string accessibleController = controllerNameDict[controllerName];

                if ((controllerName == Constants.ERROR_CONTROLLER || controllerName == Constants.HOME_CONTROLLER || controllerName == Constants.USERPROFILE_CONTROLLER)
                    && userFunctions != null && userTerminals != null)
                {
                    return true;
                }

                if (null != userFunctions && userTerminals != null)
                {
                    result = userFunctions.Contains(accessibleController);
                }
            }
            return result;
        }
    }
}