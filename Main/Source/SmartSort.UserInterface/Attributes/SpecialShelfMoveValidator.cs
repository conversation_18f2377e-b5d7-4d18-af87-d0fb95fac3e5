﻿using SmartSort.Business.Entities;
using SmartSort.UserInterface.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SmartSort.UserInterface.Attributes
{
    public class SpecialShelfMoveValidator : ValidationAttribute, IClientValidatable
    {
        #region Private Variables
        private readonly string _sourceRoutePropertyName, _sourceShelfPropertyName, _targetRoutePropertyName, _targetShelfPropertyName;
        private readonly static string[] specialShelves = { "17", "18", "19", "20", "21", "22", "A", "B", "C", "D" };
        #endregion

        #region Public Constructors
        public SpecialShelfMoveValidator(string sourceRoutePropertyName, string targeRoutePropertyName, string sourceShelfProrpertyName, string targetShelfPropertyName)
            : base()
        {
            _sourceRoutePropertyName = sourceRoutePropertyName;
            _targetRoutePropertyName = targeRoutePropertyName;
            _sourceShelfPropertyName = sourceShelfProrpertyName;
            _targetShelfPropertyName = targetShelfPropertyName;
        }
        #endregion

        #region Public Methods
        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule()
            {
                ErrorMessage = FormatErrorMessage(metadata.GetDisplayName()),
                ValidationType = "specialshelfmove"
            };
            rule.ValidationParameters.Add("specialshelfmoverule", true);
            yield return rule;
        }
        #endregion

        #region Protected Methods
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            bool isSuccess = false;
            var sourceRouteProperty = validationContext.ObjectInstance.GetType().GetProperty(_sourceRoutePropertyName);
            var targetRouteProperty = validationContext.ObjectInstance.GetType().GetProperty(_targetRoutePropertyName);
            var sourceShelfProperty = validationContext.ObjectInstance.GetType().GetProperty(_sourceShelfPropertyName);
            var targetShelfProperty = validationContext.ObjectInstance.GetType().GetProperty(_targetShelfPropertyName);
            if (sourceRouteProperty != null && targetRouteProperty != null && sourceShelfProperty != null && targetShelfProperty != null)
            {
                string sourceRoute = sourceRouteProperty.GetValue(validationContext.ObjectInstance, null) as string;
                string targetRoute = targetRouteProperty.GetValue(validationContext.ObjectInstance, null) as string;
                string sourceShelf = sourceShelfProperty.GetValue(validationContext.ObjectInstance, null) as string;
                string targetShelf = targetShelfProperty.GetValue(validationContext.ObjectInstance, null) as string;
                isSuccess = ! (!String.Equals(sourceRoute, targetRoute, StringComparison.OrdinalIgnoreCase) && specialShelves.Contains(targetShelf.ToUpper()));
            }
            return isSuccess ? ValidationResult.Success : new ValidationResult(ErrorMessageString);
        }
        #endregion
    }
}