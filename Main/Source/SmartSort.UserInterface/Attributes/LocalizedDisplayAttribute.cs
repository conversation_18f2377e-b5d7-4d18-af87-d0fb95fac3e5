﻿using System;
using System.ComponentModel;
using System.Reflection;

namespace SmartSort.UserInterface.Attributes
{
   
    [AttributeUsage(AttributeTargets.All, Inherited = false, AllowMultiple = true)]
    public class LocalizedDisplayAttribute : DisplayNameAttribute
    {
        private PropertyInfo nameProperty;
        private Type resourceType;

        public LocalizedDisplayAttribute(string displayNameKey)
            : base(displayNameKey)
        {
        }

        public Type NameResourceType
        {
            get
            {
                return resourceType;
            }
            set
            {
                resourceType = value;
                
                nameProperty = resourceType.GetProperty(base.DisplayName, BindingFlags.Static | BindingFlags.Public);
            }
        }

        public override string DisplayName
        {
            get
            {
                if (nameProperty == null)
                {
                    return base.DisplayName;
                }


                return ((string)nameProperty.GetValue(nameProperty.DeclaringType, null));
            }
        }
    }
}