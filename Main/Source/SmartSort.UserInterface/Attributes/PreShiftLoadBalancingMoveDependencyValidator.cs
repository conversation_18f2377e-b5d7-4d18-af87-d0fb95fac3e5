﻿using SmartSort.Business.Entities;
using SmartSort.UserInterface.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SmartSort.UserInterface.Attributes
{
    public class PreShiftLoadBalancingMoveDependencyValidator : ValidationAttribute, IClientValidatable
    {
        #region Private Variables
        private readonly string _moveDependencyPropertyName;
        private readonly int _countMinValue;
        #endregion

        #region Public Constructors
        public PreShiftLoadBalancingMoveDependencyValidator(string moveDependencyPropertyName, int countMinValue) : base()
        {
            _moveDependencyPropertyName = moveDependencyPropertyName;
            _countMinValue = countMinValue;
        }
        #endregion

        #region Public Methods
        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule()
            {
                ErrorMessage = FormatErrorMessage(metadata.GetDisplayName()),
                ValidationType = "movedependency"
            };
            rule.ValidationParameters.Add("countminval", _countMinValue);
            yield return rule;
        }
        #endregion

        #region Protected Methods
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            bool isSuccess = false;
            if (value is bool)
            {
                if (!((bool)value))
                {
                    var moveDependencyProperty = validationContext.ObjectInstance.GetType().GetProperty(_moveDependencyPropertyName);
                    if (moveDependencyProperty != null)
                    {
                        var customerStops = moveDependencyProperty.GetValue(validationContext.ObjectInstance, null) as List<CustomerStopModel>;
                        if (customerStops != null)
                        {
                            isSuccess = customerStops != null && customerStops.Count > 0 ? customerStops.Any(item => item.IsMoveRequired) : false;
                        }
                    }
                }
                else
                {
                    isSuccess = true;
                }
            }
            return isSuccess ? ValidationResult.Success : new ValidationResult(ErrorMessageString);
        }
        #endregion
    }
}