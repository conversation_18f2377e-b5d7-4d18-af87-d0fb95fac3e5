﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SmartSort.UserInterface.Attributes
{
    public class CountDependencyValidator : ValidationAttribute, IClientValidatable
    {
        #region Private Variables
        private readonly string _countDependencyPropertyName;
        private readonly int _countMinValue;
        #endregion

        #region Public Constructors
        public CountDependencyValidator(string countDependencyPropertyName, int countMinValue) : base()
        {
            _countDependencyPropertyName = countDependencyPropertyName;
            _countMinValue = countMinValue;
        }
        #endregion

        #region Public Methods
        public IEnumerable<ModelClientValidationRule> GetClientValidationRules(ModelMetadata metadata, ControllerContext context)
        {
            var rule = new ModelClientValidationRule()
            {
                ErrorMessage = FormatErrorMessage(metadata.GetDisplayName()),
                ValidationType = "countdependency"
            };
            rule.ValidationParameters.Add("countminval", _countMinValue);
            yield return rule;
        }
        #endregion

        #region Protected Methods
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            bool isSuccess = true;
            if (value != null && value is bool && (bool)value)
            {
                var countDependencyProperty = validationContext.GetType().GetProperty(_countDependencyPropertyName);
                if (countDependencyProperty != null)
                {
                    int count = (int)countDependencyProperty.GetValue(validationContext.ObjectInstance, null);
                    if (count > _countMinValue)
                    {
                        isSuccess = false;
                    }
                }
                else
                {
                    isSuccess = false;
                }
            }
            return isSuccess ? ValidationResult.Success : new ValidationResult(ErrorMessageString);
        }
        #endregion
    }
}