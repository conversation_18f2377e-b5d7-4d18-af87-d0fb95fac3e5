﻿using System;
using System.Web;
using System.Web.Mvc;
using SmartSort.Common;

namespace SmartSort.UserInterface.Common
{
    public class BinaryContentResult: ActionResult
    {
        #region Private Variables
        private string ContentType;
        private IRenderer renderer;
        private string FileName;
        #endregion

        #region Public Constructors  

        public BinaryContentResult(IRenderer rend, string contentType, string fileName)
        {
            renderer = rend;
            ContentType = contentType;
            this.FileName = fileName;
        }
        #endregion

        #region Public Methods
        public override void ExecuteResult(ControllerContext context)
        {
            HttpResponseBase response = null;
            if (context != null && context.HttpContext.Response != null)
            {
                response = context.HttpContext.Response;
            }
            response.Clear();
            response.ClearContent();
            response.ClearHeaders();
            response.Buffer = true;            
            
            response.ContentType = ContentType;
            response.AddHeader("Pragma", "public");
            response.AddHeader("Cache-Control", "private, max-age=60");
            response.AddHeader("Content-Transfer-Encoding", "binary");
            response.AddHeader("content-disposition", "attachment; filename=" + FileName);
            renderer.Render(response.OutputStream);
        }
        #endregion
    }
}