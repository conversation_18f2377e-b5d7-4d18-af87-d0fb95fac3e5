﻿using System.Web;

namespace SmartSort.UserInterface.Common
{
    public static class SessionManager<T>
    {
        // Gets from session; if not present, returns default(T).
        public static T GetFromSession(string sessionKey)
        {
            // Insert Code
            object o = null;
            if (HttpContext.Current.Session != null)
            {
                o = HttpContext.Current.Session[sessionKey];
            }
            if (o == null) return default(T);
            return (T)o;
        }

        // Sets the session for key and value
        public static void SetInSession(string sessionKey, T sessionValue)
        {
            HttpContext.Current.Session[sessionKey] = sessionValue;
        }

        // Removes key from the Session
        public static void RemoveFromSession(string sessionKey)
        {
            HttpContext.Current.Session.Remove(sessionKey);
        }

        // Clear values from the Session
        public static void ClearSession()
        {
            HttpContext.Current.Session.Clear();
        }
    }
}