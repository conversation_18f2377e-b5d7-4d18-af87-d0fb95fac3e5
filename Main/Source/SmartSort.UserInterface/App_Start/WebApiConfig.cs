﻿using System.Web.Http;

namespace SmartSort.UserInterface
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            if (null != config)
            {
                config.Routes.MapHttpRoute(
                    name: "Default<PERSON><PERSON>",
                    routeTemplate: "api/{controller}/{id}",
                    defaults: new { id = RouteParameter.Optional }
                );
            }

            // Uncomment the following line of code to enable query support for actions with an IQueryable or IQueryable<T> return type.
            // To avoid processing unexpected or malicious queries, use the validation settings on QueryableAttribute to validate incoming queries.
            // For more information, visit http://go.microsoft.com/fwlink/?LinkId=279712.
            //config.EnableQuerySupport();
        }
    }
}