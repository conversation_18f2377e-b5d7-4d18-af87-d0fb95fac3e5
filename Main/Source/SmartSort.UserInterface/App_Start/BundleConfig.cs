﻿using System.Web.Optimization;

namespace SmartSort.UserInterface
{
    public class BundleConfig
    {
        // For more information on Bundling, visit http://go.microsoft.com/fwlink/?LinkId=254725
        public static void RegisterBundles(BundleCollection bundles)
        {
            BundleScripts(bundles);
            BundleStyles(bundles);

#if !DEBUG
            BundleTable.EnableOptimizations = true;
#endif
        }

        #region Scripts

        private static void BundleScripts(BundleCollection bundles)
        {
            // fixes for browser issues (mainly IE)
            var fixes = new ScriptBundle("~/bundles/fixes")
                .Include("~/Scripts/modernizr-{version}.js")
                .Include("~/Scripts/respond.js");
            //.Include("~/Scripts/Application/fix.*");
            bundles.Add(fixes);

            bundles.Add(new ScriptBundle("~/bundles/libraries")
                .Include("~/Scripts/jquery-{version}.js")
                .Include("~/Scripts/jquery-ui-{version}.js")
                .Include("~/Scripts/jquery.validate*")
                .Include("~/Scripts/jquery.unobtrusive*")
                .Include("~/Scripts/jquery.watermark.js")
                .Include("~/Scripts/bootstrap.js")
                .Include("~/Scripts/JSON2.js")
                .Include("~/Scripts/fullcalendar.js")
                .Include("~/Scripts/Application/InputTextValidation_Extension.js")

                );

            bundles.Add(new ScriptBundle("~/bundles/application/home")
                .Include("~/Scripts/Application/global.*")
                .Include("~/Scripts/Application/helpers.*")
                .Include("~/Scripts/Application/root.*")
                .Include("~/Scripts/Application/home.*")
                .Include("~/Scripts/Application/validation.*")
                 .Include("~/Scripts/Application/CalendarView.*")
                );

            bundles.Add(new ScriptBundle("~/bundles/application/landing")
                .Include("~/Scripts/Application/global.*")
                .Include("~/Scripts/Application/helpers.*")
                .Include("~/Scripts/Application/root.*")
                .Include("~/Scripts/Application/landing.*")
                .Include("~/Scripts/Application/validation.*")
                );
        }

        #endregion Scripts

        #region Styles

        private static void BundleStyles(BundleCollection bundles)
        {
            // validate that the site.css is always rendered after the bootstrap.css that way site.css can
            // override values in bootstrap.css when required
            bundles.Add(new StyleBundle("~/Content/css")
                .Include("~/Content/bootstrap.css")
                .Include("~/Content/fontawesome.css")
                .Include("~/Content/site.css")
                .Include("~/Content/fullcalendar.css")
                );

            bundles.Add(new StyleBundle("~/Content/themes/base/css")
                .Include("~/Content/themes/base/jquery.ui.core.css")
                .Include("~/Content/themes/base/jquery.ui.autocomplete.css")
                );
        }

        #endregion Styles
    }
}