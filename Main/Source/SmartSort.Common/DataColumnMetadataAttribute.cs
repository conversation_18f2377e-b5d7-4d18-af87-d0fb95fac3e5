﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartSort.Common
{
    public class DataColumnMetadataAttribute : Attribute
    {
        #region Public Properties
        public string DataColumnName { get; private set; }
        public bool IsBooleanMapping { get; private set; }
        public bool IsRegexMatch { get; private set; }
        #endregion

        public DataColumnMetadataAttribute(string dataColumnName, bool isBooleanMapping = false, bool isRegexMatch = false)
        {
            this.DataColumnName = dataColumnName;
            this.IsBooleanMapping = isBooleanMapping;
            this.IsRegexMatch = isRegexMatch;
        }
    }
}
