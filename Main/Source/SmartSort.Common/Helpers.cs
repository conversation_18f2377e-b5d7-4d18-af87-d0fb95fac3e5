﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;

namespace SmartSort.Common
{
    public static class Helpers
    {
        public static bool IsCurrentLanguageEnglish()
        {
            return IsEnglish(GetCurrentLanguage());
        }

        public static bool IsCurrentLanguageFrench()
        {
            return IsFrench(GetCurrentLanguage());
        }

        public static bool IsFrench(string languageCode)
        {
            bool retVal = languageCode.ValueExists() && languageCode.ToUpper().Equals(Constants.LANGUAGE_FRENCH);
            return retVal;
        }

        public static bool IsEnglish(string languageCode)
        {
            return !IsFrench(languageCode);
        }

        public static string NullToEmptyString(string text)
        {
            return text.ValueExists() ? text.Trim() : string.Empty;
        }

        public static string NullToEmptyString(int? inValue)
        {
            return inValue.HasValue ? inValue.ToString() : string.Empty;
        }

        public static bool IsCanadaCountryCode(string countryCode)
        {
            return (countryCode.ValueExists() && countryCode.ToUpper().Equals(Constants.COUNTRY_CANADA_CODE));
        }

        public static string StripPostalZipCode(string pcode)
        {
            string retVal = string.Empty;
            if (pcode.ValueExists())
            {
                /*trim white spaces and spaces between */
                retVal = pcode.ToUpper().Replace(" ", string.Empty).Replace("-", string.Empty).Trim();
            }
            return retVal;
        }

        public static string StripPhoneNumber(string phoneText)
        {
            string retVal = string.Empty;
            if (phoneText.ValueExists())
            {
                retVal = Regex.Replace(phoneText, @"[\D]", string.Empty);
            }
            return retVal;
        }

        public static bool IsValidCanadianPostalCodeFormat(string pcode)
        {
            bool retVal = false;
            if (pcode.ValueExists())
            {
                //trim white spaces and spaces between
                string postalCode = StripPostalZipCode(pcode);

                //	Check postal code format for A9A9A9
                if (postalCode.Length < Constants.MIN_CDN_POSTAL_CODE_LENGTH ||
                    postalCode.Length > Constants.MAX_CDN_POSTAL_CODE_LENGTH)
                {
                }
                else
                {
                    Regex rx = new Regex(Constants.REGEX_POSTALCODE);
                    retVal = rx.IsMatch(postalCode);
                }
            }
            return retVal;
        }

        public static bool IsValidUSPostalCodeFormat(string pcode)
        {
            bool retVal = false;
            if (pcode.ValueExists())
            {
                //trim white spaces and spaces between
                string postalCode = StripPostalZipCode(pcode);
                Regex rx = new Regex(Constants.REGEX_US_POSTALCODE);
                retVal = rx.IsMatch(postalCode);
            }
            return retVal;
        }

        public static bool IsValidZipCodeFormat(string zipCode)
        {
            bool retVal = false;
            if (zipCode.ValueExists())
            {
                //remove spaces and "-"
                zipCode = StripPostalZipCode(zipCode);

                //check whether the length is 5-digit or 9-digit long.
                Regex rx = new Regex(Constants.REGEX_ZIPCODE);
                retVal = rx.IsMatch(zipCode);
            }
            return retVal;
        }

        /// <summary>
        /// Compares content of the two specified string values, ignoring case
        /// </summary>
        /// <param name="string1">Value of the first string</param>
        /// <param name="string2">Value of the second string</param>
        /// <returns>A boolean value indicating whether the 2 specified strings are the same</returns>
        public static bool StringCompare(string string1, string string2)
        {
            return StringCompare(string1, string2, true);
        }

        public static bool StringCompare(string string1, string string2, bool ignoreCase)
        {
            StringComparison compareType = StringComparison.InvariantCulture;
            if (ignoreCase) compareType = StringComparison.InvariantCultureIgnoreCase;
            return (string.Compare(NullToEmptyString(string1), NullToEmptyString(string2), compareType) == 0);
        }

        private static string GetShortDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.SHORT_DATE_FORMAT_FRENCH :
                Constants.SHORT_DATE_FORMAT_ENGLISH;
        }

        private static string GetMediumDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.MEDIUM_DATE_FORMAT_FRENCH :
                Constants.MEDIUM_DATE_FORMAT_ENGLISH;
        }

        private static string GetLongDateFormat()
        {
            return IsEnvironmentFrench() ?
                Constants.LONG_DATE_FORMAT_FRENCH :
                Constants.LONG_DATE_FORMAT_ENGLISH;
        }

        public static string FormatShortDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetShortDateFormat()) : string.Empty;
        }

        public static string FormatMediumDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetMediumDateFormat()) : string.Empty;
        }

        public static string FormatLongDate(DateTime? date)
        {
            return date != null ? date.Value.ToString(GetLongDateFormat()) : string.Empty;
        }

        public static string GetFormattedShelf(string s)
        {
            string s1 = s;
            int number;
            if (!String.IsNullOrWhiteSpace(s) && !s.StartsWith("0") && int.TryParse(s, out number) && number >= 1 && number <= 9)
            {
                s1 = "0" + s;
            }
            return s1;
        }

        public static string GetDaySuffixEnglish(int day)
        {
            switch (day)
            {
                case 1:
                case 21:
                case 31:
                    return "st";

                case 2:
                case 22:
                    return "nd";

                case 3:
                case 23:
                    return "rd";

                default:
                    return "th";
            }
        }

        public static string GetCurrentLanguage()
        {
            string retVal = Constants.LANGUAGE_ENGLISH;
            if (IsEnvironmentFrench()) retVal = Constants.LANGUAGE_FRENCH;
            return retVal;
        }

        /// <summary>
        /// Returns True if the current culture is French
        /// </summary>
        /// <returns>Boolean</returns>
        public static bool IsEnvironmentFrench()
        {
            return Thread.CurrentThread.CurrentUICulture.TwoLetterISOLanguageName.Equals("fr");
        }

        /// <summary>
        /// Format postal code for displaying.
        /// </summary>
        /// <param name="pcode">the postal code</param>
        /// <returns>the formatted postal code</returns>
        public static string FormatPostalCode(string pcode)
        {
            string retVal = string.Empty;
            if (pcode.ValueExists())
            {
                retVal = StripPostalZipCode(pcode);
                if (retVal.Length > 3)
                {
                    retVal = retVal.Substring(0, 3) + " " + retVal.Substring(3);
                }
            }
            return retVal;
        }

        public static void ParsePhone(string phoneNumber, out string outPhoneCountryCode, out string outPhoneAreaCode, out string outPhoneNumber)
        {
            string dummy;
            ParsePhone(phoneNumber, out outPhoneCountryCode, out outPhoneAreaCode, out outPhoneNumber, out dummy);
        }

        public static void ParsePhone(string phoneNumber, out string outPhoneCountryCode, out string outPhoneAreaCode, out string outPhoneNumber, out string outExtension)
        {
            //Initialize Out Variables
            outPhoneCountryCode = string.Empty;
            outPhoneAreaCode = string.Empty;
            outPhoneNumber = string.Empty;
            outExtension = string.Empty;

            if (phoneNumber.ValueExists())
            {
                outPhoneCountryCode = Constants.PHONE_COUNTRY_CODE_CANADA_US;
                string tempPhone = StripPhoneNumber(phoneNumber);

                int areaCodeLength = 3;
                int phoneLength = 7;
                int extLength = 6;

                if (tempPhone.Length >= areaCodeLength)
                {
                    //Extract Area Code [First 3 Digits]
                    outPhoneAreaCode = tempPhone.Substring(0, areaCodeLength);

                    //Chop Off First 3 Digits
                    tempPhone = tempPhone.Substring(areaCodeLength);

                    if (tempPhone.Length >= phoneLength)
                    {
                        //Extract Phone [Next 7 Digits]
                        outPhoneNumber = tempPhone.Substring(0, phoneLength);

                        //Chop Off 7 Digits
                        tempPhone = tempPhone.Substring(phoneLength);

                        if (tempPhone.Length >= extLength)
                        {
                            //Extract Extension
                            outExtension = tempPhone.Substring(0, extLength);

                            //Chop Off 6 Digits
                            tempPhone = tempPhone.Substring(extLength);
                        }
                        else
                        {
                            //Leftovers into Extension
                            outExtension = tempPhone;
                        }
                    }
                    else
                    {
                        outPhoneNumber = tempPhone;
                    }
                }
                else
                {
                    outPhoneNumber = tempPhone;
                }
            }
        }

        public static bool IsNullOrEmptyList<T>(List<T> items)
        {
            return items == null || !items.Any();
        }
        /// <summary>
        /// Convert Date time offset to string 
        /// </summary>
        /// <param name="dateOffset">dateOffset</param>
        /// <returns>string</returns>
        public static string ConvertDateTimeOffsetToString(DateTimeOffset dateOffset)
        {
            return dateOffset.ToString("yyyy-MM-dd HH:mm:ss.fff zzz");
        }
        /// <summary>
        /// Convert DateTimeOffset To DateTime String
        /// </summary>
        /// <param name="dateOffset"></param>
        /// <returns></returns>
        public static string ConvertDateTimeOffsetToDateTimeString(DateTimeOffset ? dateOffset)
        {
            if (dateOffset.HasValue)
           {
               return dateOffset.Value.ToString("yyyy-MM-dd HH:mm:ss");
           }
           else
            {
                return "";
           }
                
           
        
        }
        /// <summary>
        /// Convert Date time to string 
        /// </summary>
        /// <returns>string</returns>
        public static string ConvertDateTimeToString(DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// Convert Date time to string with suffix
        /// </summary>
        /// <returns>string</returns>
        public static string ConvertDateTimeToString(string date)
        {
            string inputDate = string.Empty;
            if (date.Contains('/'))
            {
                inputDate = date;
            }
            else if (date.Contains('-'))
            {
                inputDate = date.ConvertDateStringToddMMYYYYFormat();
            }
            //var sortPlanDate = Convert.ToDateTime(date);
            var dateTimeCalendar = DateTime.ParseExact(inputDate, "dd/MM/yyyy", CultureInfo.InvariantCulture);
            return string.Format("{0} {1}{2}, {3}",
                dateTimeCalendar.ToString("MMMM"),
                dateTimeCalendar.Day,
                GetDaySuffixEnglish(dateTimeCalendar.Day),
                dateTimeCalendar.Year);
        }
    }
}