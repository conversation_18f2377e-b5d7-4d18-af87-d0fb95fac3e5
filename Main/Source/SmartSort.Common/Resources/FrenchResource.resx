﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="DateFormatEstimate_9AM" xml:space="preserve">
    <value>{0}&lt;br&gt;9h</value>
  </data>
  <data name="DateFormatEstimate_10:30AM" xml:space="preserve">
    <value>{0}&lt;br&gt;10h30</value>
  </data>
  <data name="DateFormatEstimate_NOON" xml:space="preserve">
    <value>{0}&lt;br&gt;12h (midi)</value>
  </data>
  <data name="DateFormatEstimate_EVENING" xml:space="preserve">
    <value>{0}&lt;br&gt;Entre 17h30 et 21h</value>
  </data>
  <data name="DateFormatEstimate_ENDOFDAY" xml:space="preserve">
    <value>{0}&lt;br&gt;20h</value>
  </data>
  <data name="PCH_BEYOND_POINT_DEST" xml:space="preserve">
    <value>Destination Au-Delà</value>
  </data>
  <data name="PCH_BEYOND_POINT_ORIGIN" xml:space="preserve">
    <value>Origine Au-Delà</value>
  </data>
  <data name="PCH_CHAIN_OF_SIGNATURE" xml:space="preserve">
    <value>Chaîne de signatures</value>
  </data>
  <data name="PCH_COLLECT" xml:space="preserve">
    <value>Surcharge Port Dû</value>
  </data>
  <data name="PCH_DANGEROUS_GOODS" xml:space="preserve">
    <value>Marchandises Dangereuses</value>
  </data>
  <data name="PCH_DECLARED_VALUE" xml:space="preserve">
    <value>Valeur Déclarée</value>
  </data>
  <data name="PCH_EXPRESS_CHEQUE" xml:space="preserve">
    <value>ExpressCheque</value>
  </data>
  <data name="PCH_FUEL" xml:space="preserve">
    <value>Supplément de Carburant</value>
  </data>
  <data name="PCH_LOW_DENSITY_DEST" xml:space="preserve">
    <value>Zone Résidentielle</value>
  </data>
  <data name="PCH_LOW_DENSITY_ORIGIN" xml:space="preserve">
    <value>Cueillette résidentielle</value>
  </data>
  <data name="PCH_MULTI_PIECE" xml:space="preserve">
    <value>Multipièce</value>
  </data>
  <data name="PCH_RES_SIG_DOM" xml:space="preserve">
    <value>Signature requise liv. Résiden</value>
  </data>
  <data name="PCH_RES_SIG_INTL" xml:space="preserve">
    <value>Signature requise liv. rés.</value>
  </data>
  <data name="PCH_SATURDAY_DELIVERY" xml:space="preserve">
    <value>Service Le Samedi</value>
  </data>
  <data name="PCH_SATURDAY_PICKUP" xml:space="preserve">
    <value>Cueillette Le Samedi</value>
  </data>
  <data name="PCH_SPECIAL_HANDLING" xml:space="preserve">
    <value>Manutention spéciale</value>
  </data>
  <data name="PCH_THIRD_PARTY" xml:space="preserve">
    <value>Supplément pour tierce partie</value>
  </data>
  <data name="floorLabel" xml:space="preserve">
    <value>Étage</value>
  </data>
  <data name="suiteLabel" xml:space="preserve">
    <value>Bureau</value>
  </data>
  <data name="extensionLabel" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="taxNumberLabel" xml:space="preserve">
    <value>Numéro de taxe</value>
  </data>
  <data name="entryCodeLabel" xml:space="preserve">
    <value>Code d’entrée</value>
  </data>
  <data name="LocationTimeFormat" xml:space="preserve">
    <value>{0} et {1}</value>
  </data>
  <data name="dayOfWeekMonday" xml:space="preserve">
    <value>Lundi</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Fermé entre</value>
  </data>
  <data name="dayOfWeekFriday" xml:space="preserve">
    <value>Vendredi</value>
  </data>
  <data name="dayOfWeekSaturday" xml:space="preserve">
    <value>Samedi</value>
  </data>
  <data name="dayOfWeekSunday" xml:space="preserve">
    <value>Dimanche</value>
  </data>
  <data name="dayOfWeekThursday" xml:space="preserve">
    <value>Jeudi</value>
  </data>
  <data name="dayOfWeekTuesday" xml:space="preserve">
    <value>Mardi</value>
  </data>
  <data name="dayOfWeekWednesday" xml:space="preserve">
    <value>Mercredi</value>
  </data>
  <data name="HeaderDay" xml:space="preserve">
    <value>Jour</value>
  </data>
  <data name="HeaderOpenInfo" xml:space="preserve">
    <value>Ouvert</value>
  </data>
  <data name="HeaderClosedInfo" xml:space="preserve">
    <value>Fermé entre</value>
  </data>
  <data name="HeaderOpenCloseException" xml:space="preserve">
    <value>Exceptions</value>
  </data>
  <data name="HST" xml:space="preserve">
    <value>TPS/TVH</value>
  </data>
  <data name="GST" xml:space="preserve">
    <value>TPS/TVH</value>
  </data>
  <data name="PST" xml:space="preserve">
    <value>TVQ</value>
  </data>
</root>