﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="QS_ERROR_GENERIC" xml:space="preserve">
    <value>An Error Has Occured in the Integration Layer.</value>
  </data>
  <data name="QS_ERROR_PIN_REQUIRED" xml:space="preserve">
    <value>PIN is required.</value>
  </data>
  <data name="QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED" xml:space="preserve">
    <value>Pickup Confirmation Number is reuiqred.</value>
  </data>
  <data name="QS_ERROR_PICKUP_REQUIRED" xml:space="preserve">
    <value>Pickup Information is required.</value>
  </data>
  <data name="QS_ERROR_PICKUP_INVALID_TYPE" xml:space="preserve">
    <value>Invalid Pickup Type.</value>
  </data>
  <data name="QS_ERROR_POSTALCODE_REQUIRED" xml:space="preserve">
    <value>Postal Code is required.</value>
  </data>
  <data name="QS_ERROR_COUNTRYCODE_REQUIRED" xml:space="preserve">
    <value>Country Code is required.</value>
  </data>
  <data name="QS_ERROR_PROVINCECODE_REQUIRED" xml:space="preserve">
    <value>Province Code is required.</value>
  </data>
  <data name="QS_ERROR_CITY_REQUIRED" xml:space="preserve">
    <value>City is required.</value>
  </data>
  <data name="QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS" xml:space="preserve">
    <value>One or more addresses are invalid.</value>
  </data>
  <data name="QS_ERROR_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND" xml:space="preserve">
    <value>Country Not Found {0}.</value>
  </data>
  <data name="QS_ERROR_INVALID_POSTAL_CODE" xml:space="preserve">
    <value>The postal code indicated {0} is outside of our service area. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED" xml:space="preserve">
    <value>Postal Code {0}, is only valid for {1}, {2}.</value>
  </data>
  <data name="QS_ERROR_FROM_ADDRESS_REQUIRED" xml:space="preserve">
    <value>From Address is required.</value>
  </data>
  <data name="QS_ERROR_TO_ADDRESS_REQUIRED" xml:space="preserve">
    <value>To Address is required.</value>
  </data>
  <data name="QS_INVALID_CREDIT_CARD_NUMBER" xml:space="preserve">
    <value>Invalid Credit Card Number.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED" xml:space="preserve">
    <value>From City is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED" xml:space="preserve">
    <value>From Province is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED" xml:space="preserve">
    <value>From Postal Code is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_CITY_REQUIRED" xml:space="preserve">
    <value>To City is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED" xml:space="preserve">
    <value>To Province is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED" xml:space="preserve">
    <value>To Postal Code is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_PRODUCT_REQUIRED" xml:space="preserve">
    <value>Product is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_ESTIMATE_WEIGHT_REQUIRED" xml:space="preserve">
    <value>Weight is required to perform an Estimate.</value>
  </data>
  <data name="QS_ERROR_FORCED_HOLD_FOR_PICKUP" xml:space="preserve">
    <value>Receiver Postal Code {0} is not a deliverable address. Please select a valid Hold For Pickup Address.</value>
  </data>
  <data name="QS_CREATE_SHIPMENT_FAILED" xml:space="preserve">
    <value>Create Shipment Failed.</value>
  </data>
  <data name="QS_ERROR_CONFIRMATION_NO_REQUIRED" xml:space="preserve">
    <value>Pickup COnfirmation No is required.</value>
  </data>
  <data name="100468" xml:space="preserve">
    <value>We were unable to process your transaction as authorization to charge your credit card was denied. Please call 1-888-SHIP-123 or start a Live Chat for assistance.</value>
  </data>
  <data name="100532" xml:space="preserve">
    <value>Please enter a valid credit card number.</value>
  </data>
  <data name="100514" xml:space="preserve">
    <value>Your package exceeds maximum weight for the selected service. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="100512" xml:space="preserve">
    <value>Your package exceeds maximum dimensions. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="100509" xml:space="preserve">
    <value>Your package exceeds maximum dimensions. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="100467" xml:space="preserve">
    <value>Your package exceeds maximum weight for the selected service. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="100704" xml:space="preserve">
    <value>We've detected that your address contains additional information, such as a P.O. Box or Rural Route. Please enter this information as additional address details.</value>
  </data>
  <data name="100699" xml:space="preserve">
    <value>We are unable to schedule a pickup during the time provided. Please adjust your pickup time or drop off your package at a Purolator Shipping Centre.</value>
  </data>
  <data name="100768" xml:space="preserve">
    <value>Pickups cannot be scheduled on Sundays. Please select dropoff to locate the nearest Purolator Shipping Centre.</value>
  </data>
  <data name="100702" xml:space="preserve">
    <value>The postal code is outside of our pickup service area. Please call 1-888-SHIP-123 or drop off your package at a Purolator Shipping Centre.</value>
  </data>
  <data name="100497" xml:space="preserve">
    <value>We are unable to process your transaction at this time. Please try again or call 1-888-SHIP-123 for assistance.</value>
  </data>
  <data name="100759" xml:space="preserve">
    <value>Your available delivery options have changed since you began creating your shipment. Please provide your shipment details again. Thank you.</value>
  </data>
  <data name="100760" xml:space="preserve">
    <value>Your available delivery options have changed since you began creating your shipment. Please provide your shipment details again. Thank you.</value>
  </data>
  <data name="100632" xml:space="preserve">
    <value>The sender's postal code is outside our service area. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="100700" xml:space="preserve">
    <value>We are unable to schedule a pickup during the time provided. Please adjust your pickup time or drop off your package at a Purolator Shipping Centre.</value>
  </data>
  <data name="100432" xml:space="preserve">
    <value>We were unable to process your cancellation request. Please call 1-888-SHIP-123 or use Live Chat for assistance.</value>
  </data>
  <data name="100769" xml:space="preserve">
    <value>Shipments created before today cannot be cancelled. Please call 1-888-SHIP-123 or start a Live Chat.</value>
  </data>
  <data name="QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR" xml:space="preserve">
    <value>We are unable to process your transaction as authorization to charge your card has been denied.  Please call 1-888-SHIP-123 or Live Chat for Assistance</value>
  </data>
</root>