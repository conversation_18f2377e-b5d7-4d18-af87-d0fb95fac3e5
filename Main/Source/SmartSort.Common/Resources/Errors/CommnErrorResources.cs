﻿using System.Reflection;
using System.Resources;

namespace SmartSort.Common.Resources.Errors
{
    public class CommonErrorResources
    {
        private static ResourceManager GetResourceManager(string languageCode)
        {
            var resourceManager = Helpers.IsFrench(languageCode) ? 
                new ResourceManager("SmartSort.Common.Resources.Errors.FrenchResource", Assembly.GetExecutingAssembly()) : 
                new ResourceManager("SmartSort.Common.Resources.Errors.EnglishResource", Assembly.GetExecutingAssembly());
            return resourceManager;
        }

        public static bool ResourceExists(ResourceManager rm, string inKey, string lanuageCode)
        {
            bool retVal = false;
            if (rm != null)
            {
                object resourceObject = rm.GetObject(inKey);
                if (resourceObject != null)
                {
                    retVal = true;
                }
            }
            return retVal;
        }

        public static bool ResourceExists(string inKey, string lanuageCode)
        {
            bool retVal = false;
            ResourceManager rm = GetResourceManager(lanuageCode);
            if (rm != null)
            {
                retVal = ResourceExists(rm, inKey, lanuageCode);
            }
            return retVal;
        }

        public static string GetResourceString(string inKey, string languageCode)
        {
            string retVal = string.Empty;
            ResourceManager resourceManager = GetResourceManager( languageCode );
            if (resourceManager != null)
            {
                if (ResourceExists(resourceManager, inKey, languageCode))
                {
                    object resourceObject = resourceManager.GetObject(inKey);
                    if (resourceObject != null)
                    {
                        retVal = resourceManager.GetString(inKey);
                    }
                }
            }
            return retVal;
        }        

    }

}
