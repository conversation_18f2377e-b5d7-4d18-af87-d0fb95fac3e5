﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.1026
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace SmartSort.Common.Resources.Errors {
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [DebuggerNonUserCode()]
    [CompilerGenerated()]
    internal class EnglishResource {
        
        private static ResourceManager resourceMan;
        
        private static CultureInfo resourceCulture;
        
        [SuppressMessage("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal EnglishResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static ResourceManager ResourceManager {
            get {
                if (ReferenceEquals(resourceMan, null)) {
                    ResourceManager temp = new ResourceManager("SmartSort.Common.Resources.Errors.EnglishResource", typeof(EnglishResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [EditorBrowsable(EditorBrowsableState.Advanced)]
        internal static CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We were unable to process your cancellation request. Please call 1-888-SHIP-123 or use Live Chat for assistance..
        /// </summary>
        internal static string _100432 {
            get {
                return ResourceManager.GetString("100432", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your package exceeds maximum weight for the selected service. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100467 {
            get {
                return ResourceManager.GetString("100467", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We were unable to process your transaction as authorization to charge your credit card was denied. Please call 1-888-SHIP-123 or start a Live Chat for assistance..
        /// </summary>
        internal static string _100468 {
            get {
                return ResourceManager.GetString("100468", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are unable to process your transaction at this time. Please try again or call 1-888-SHIP-123 for assistance..
        /// </summary>
        internal static string _100497 {
            get {
                return ResourceManager.GetString("100497", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your package exceeds maximum dimensions. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100509 {
            get {
                return ResourceManager.GetString("100509", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your package exceeds maximum dimensions. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100512 {
            get {
                return ResourceManager.GetString("100512", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your package exceeds maximum weight for the selected service. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100514 {
            get {
                return ResourceManager.GetString("100514", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a valid credit card number..
        /// </summary>
        internal static string _100532 {
            get {
                return ResourceManager.GetString("100532", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The sender&apos;s postal code is outside our service area. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100632 {
            get {
                return ResourceManager.GetString("100632", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are unable to schedule a pickup during the time provided. Please adjust your pickup time or drop off your package at a Purolator Shipping Centre..
        /// </summary>
        internal static string _100699 {
            get {
                return ResourceManager.GetString("100699", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are unable to schedule a pickup during the time provided. Please adjust your pickup time or drop off your package at a Purolator Shipping Centre..
        /// </summary>
        internal static string _100700 {
            get {
                return ResourceManager.GetString("100700", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The postal code is outside of our pickup service area. Please call 1-888-SHIP-123 or drop off your package at a Purolator Shipping Centre..
        /// </summary>
        internal static string _100702 {
            get {
                return ResourceManager.GetString("100702", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We&apos;ve detected that your address contains additional information, such as a P.O. Box or Rural Route. Please enter this information as additional address details..
        /// </summary>
        internal static string _100704 {
            get {
                return ResourceManager.GetString("100704", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your available delivery options have changed since you began creating your shipment. Please provide your shipment details again. Thank you..
        /// </summary>
        internal static string _100759 {
            get {
                return ResourceManager.GetString("100759", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your available delivery options have changed since you began creating your shipment. Please provide your shipment details again. Thank you..
        /// </summary>
        internal static string _100760 {
            get {
                return ResourceManager.GetString("100760", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickups cannot be scheduled on Sundays. Please select dropoff to locate the nearest Purolator Shipping Centre..
        /// </summary>
        internal static string _100768 {
            get {
                return ResourceManager.GetString("100768", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipments created before today cannot be cancelled. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string _100769 {
            get {
                return ResourceManager.GetString("100769", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We are unable to process your transaction as authorization to charge your card has been denied.  Please call 1-888-SHIP-123 or Live Chat for Assistance.
        /// </summary>
        internal static string QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR {
            get {
                return ResourceManager.GetString("QS_CLIENT_INTEGRATION_CREDIT_CARD_DATA_POWER_ERROR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Shipment Failed..
        /// </summary>
        internal static string QS_CREATE_SHIPMENT_FAILED {
            get {
                return ResourceManager.GetString("QS_CREATE_SHIPMENT_FAILED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City is required..
        /// </summary>
        internal static string QS_ERROR_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup COnfirmation No is required..
        /// </summary>
        internal static string QS_ERROR_CONFIRMATION_NO_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_CONFIRMATION_NO_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Code is required..
        /// </summary>
        internal static string QS_ERROR_COUNTRYCODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_COUNTRYCODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From City is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Postal Code is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_POSTAL_CODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Province is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_FROM_PROVINCE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_PRODUCT_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_PRODUCT_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To City is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_CITY_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_CITY_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Postal Code is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_POSTAL_CODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Province is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_TO_PROVINCE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight is required to perform an Estimate..
        /// </summary>
        internal static string QS_ERROR_ESTIMATE_WEIGHT_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_ESTIMATE_WEIGHT_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiver Postal Code {0} is not a deliverable address. Please select a valid Hold For Pickup Address..
        /// </summary>
        internal static string QS_ERROR_FORCED_HOLD_FOR_PICKUP {
            get {
                return ResourceManager.GetString("QS_ERROR_FORCED_HOLD_FOR_PICKUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Address is required..
        /// </summary>
        internal static string QS_ERROR_FROM_ADDRESS_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_FROM_ADDRESS_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An Error Has Occured in the Integration Layer..
        /// </summary>
        internal static string QS_ERROR_GENERIC {
            get {
                return ResourceManager.GetString("QS_ERROR_GENERIC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The postal code indicated {0} is outside of our service area. Please call 1-888-SHIP-123 or start a Live Chat..
        /// </summary>
        internal static string QS_ERROR_INVALID_POSTAL_CODE {
            get {
                return ResourceManager.GetString("QS_ERROR_INVALID_POSTAL_CODE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup Confirmation Number is reuiqred..
        /// </summary>
        internal static string QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_CONFIRMATION_NUMBER_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Pickup Type..
        /// </summary>
        internal static string QS_ERROR_PICKUP_INVALID_TYPE {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_INVALID_TYPE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pickup Information is required..
        /// </summary>
        internal static string QS_ERROR_PICKUP_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PICKUP_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PIN is required..
        /// </summary>
        internal static string QS_ERROR_PIN_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PIN_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code is required..
        /// </summary>
        internal static string QS_ERROR_POSTALCODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_POSTALCODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Province Code is required..
        /// </summary>
        internal static string QS_ERROR_PROVINCECODE_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_PROVINCECODE_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Not Found {0}..
        /// </summary>
        internal static string QS_ERROR_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND {
            get {
                return ResourceManager.GetString("QS_ERROR_SERVICE_AVAILABILITY_COUNTRY_NOT_FOUND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One or more addresses are invalid..
        /// </summary>
        internal static string QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS {
            get {
                return ResourceManager.GetString("QS_ERROR_SERVICE_DIRECTORY_INVALID_ADDRESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Address is required..
        /// </summary>
        internal static string QS_ERROR_TO_ADDRESS_REQUIRED {
            get {
                return ResourceManager.GetString("QS_ERROR_TO_ADDRESS_REQUIRED", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Credit Card Number..
        /// </summary>
        internal static string QS_INVALID_CREDIT_CARD_NUMBER {
            get {
                return ResourceManager.GetString("QS_INVALID_CREDIT_CARD_NUMBER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postal Code {0}, is only valid for {1}, {2}..
        /// </summary>
        internal static string QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED {
            get {
                return ResourceManager.GetString("QS_SERVICE_AVAILABILITY_SUGGESTED_ADDRESS_RETURNED", resourceCulture);
            }
        }
    }
}
