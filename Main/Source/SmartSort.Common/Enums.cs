﻿namespace SmartSort.Common
{
    public enum LogPriority
    {
        /// <summary>
        /// High - error
        /// </summary>
        High = 5,

        /// <summary>
        /// Medium - warning
        /// </summary>
        Medium = 3,

        /// <summary>
        /// Low - informational
        /// </summary>
        Low = 1
    };

    public enum SortPlanActionEnum
    {
        /// <summary>
        /// Create new sort plan.
        /// </summary>
        Create = 0,

        /// <summary>
        /// Edit any sort plan.
        /// </summary>
        Edit = 1,

        /// <summary>
        /// Copy any sort plan.
        /// </summary>
        Clone = 2
    };
}