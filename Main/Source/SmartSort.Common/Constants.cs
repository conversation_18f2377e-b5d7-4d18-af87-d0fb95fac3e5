﻿using System.Collections.Generic;
namespace SmartSort.Common
{
    public class Constants
    {
        public const string CONFIG_COOKIE_DOMAIN = "COOKIE_DOMAIN_PUROLATOR_COM";

        #region UserFunction
        public const string constATR = "ATR";
        public const string constCM = "CM";
        public const string constSHM = "SHM";
        public const string constSPC = "SPC";
        public const string constSPM = "SPM";
        public const string constTCM = "TCM";
        public const string constUM = "UM";
        public const string constHR = "HR";
        public const string constGC = "GC";
        #endregion

        // Language Constants
        public const string LANGUAGE_ENGLISH = "EN";

        public const string LANGUAGE_FRENCH = "FR";
        public const string LANGUAGE_DEFAULT = "EN";

        //Smart Sort Language Constants
        public const string LANGUAGE_SmartSort_FRENCH = "F";

        public const string LANGUAGE_SmartSort_ENGLISH = "E";

        //Report Printing Default

        // Yes/No Constants
        public const string YES_CODE = "Y";

        public const string NO_CODE = "N";
        public const string YES = "YES";
        public const string NO = "NO";

        public const string GreenColor = "Green";
        public const string BlueColor = "Blue";
        public const string OrangeColor = "#8B0000";

        public const string DefaultSortPlanAssignment = "D";
        public const string ManualSortPlanAssignment = "M";

        public const string ValidateStickySessionCookieKey = "SERVER_NAME";
        public const string ValidateStickySessionConfigKey = "ValidateStickySession";

        //Login Constants
        public const string DomainControllerConfigKey = "DomainController";
        public const string AllowedUserEmailDomainsConfigKey = "AllowedUserEmailDomains";

        public const string LOGIN = "LOGIN";
        public const string SELECT_TERMINAL = "SELECTTERMINAL";
        public const string ERROR = "ERROR";
        public const string LOGOUT = "LOGOUT";
        public const string LOGIN_URL = "~/Account/Login";
        public const string SamAccountName = "sAMAccountName";
        public const string DistinguishedName = "distinguishedName";
        public const string DefaultNamingContext = "defaultNamingContext";
        public const string REDIRECT_URL = "RedirectURL";

        //Canada Us phone constants
        public const string PHONE_COUNTRY_CODE_CANADA_US = "1";

        public const string COUNTRY_CANADA_CODE = "CA";
        public const int MIN_DOM_US_PHONE_LENGTH = 7;
        public const int MAX_DOM_US_PHONE_LENGTH = 7;

        //User Related Information
        public const string TERMINAL_TIMEZONEINFO = "TerminalTimeZoneInfo";
        public const string TERMINALS_TO_SELECT = "TERMINALS_TO_SELECT";
        public const string TERMINAL_NUMBER = "TerminalNumber";
        public const string TERMINAL_NUMBER_CITY = "TerminalNumber_City";
        public const string FULL_NAME = "FullName";
        public const string USER_NAME = "User Name";
        public const string USER_ROLES = "User Roles";
        public const string USER_ID = "User ID";
        public const string SAP_USER_ID = "SAP_USER_ID";
        public const string USER_FUNCTION = "USER_FUNCTION";
        public const string USER_TERMINALS = "USER_TERMINAL";
        public const string USER_DISABLE = "Disable";
        public const string ERROR_VALIDATE_TERMINAL = "This Terminal does not belong to the User";

        # region Controller List
        public const string ADDRESS_CONTROLLER = "Address";
        public const string REPORTS_CONTROLLER = "Reports";
        public const string SHIFTMANAGEMENT_CONTROLLER = "ShiftManagement";
        public const string SORTPLAN_CONTROLLER = "SortPlan";
        public const string ACCOUNT_CONTROLLER = "Account";
        public const string ERROR_CONTROLLER = "Error";
        public const string HOME_CONTROLLER = "Home";
        public const string USERPROFILE_CONTROLLER = "UserProfile";
        public const string SORTPLANMANAGEMENT_CONTROLLER = "SortPlanManagement";
        public const string TERMINALCONFIGURATION_CONTROLLER = "TerminalConfiguration";
        public const string ROUTEPLANMANAGEMENT_CONTROLLER = "RoutePlanManagement";
        public const string PIN_HISTORY_CONTROLLER = "PinHistory";
        public const string MOVE_HISTORY_CONTROLLER = "MoveHistory";
        public const string GLOBAL_CONFIGURATION_CONTROLLER = "GlobalConfiguration";     
        
        #endregion

        #region Regular Expressions

        // Email
        public const string REGEX_EMAIL = @"^(([a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]|[a-zA-Z0-9])@[a-zA-Z0-9][\w\.-]*[a-zA-Z0-9]\.[a-zA-Z][a-zA-Z\.]*[a-zA-Z])\s*$";

        // Postal Code
        public const string REGEX_GUI_POSTALCODE = @"^([A-Za-z]\d[A-Za-z]\ {0,1}\-{0,1}\d[A-Za-z]\d)$";

        public const string REGEX_GUI_US_CND_POSTALCODE = @"^\d{5}(-\d{4})?$|^([a-zA-Z]\d[a-zA-Z]( )?\d[a-zA-Z]\d)$";
        public const string REGEX_POSTALCODE = @"[A-Z][0-9][A-Z][0-9][A-Z][0-9]";
        public const string REGEX_US_POSTALCODE = @"^\d{5}(-\d{4})?$";
        public const string REGEX_ZIPCODE = @"^\d{5}$|^\d{9}$";
        public const string REGEX_POSITIVE_INTEGER = @"^\d+$";
        public const string REGEX_NOHTML_TAGS = @"^[^<>,<|>]+$";
        public const string REGEX_NOHTML_TAGS_AllowAlpha = @"^[^<>,<|>]+$|[0-9]";
        // Phone
        public const string REGEX_PHONE = @"^\s*\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})\s*$";

        // Phone Extension
        public const string REGEX_PHONE_EXT = @"^\d{0,6}$";

        #endregion

        public const string SHORT_DATE_FORMAT_ENGLISH = "MMMM d";
        public const string SHORT_DATE_FORMAT_FRENCH = "d MMMM";

        public const string MEDIUM_DATE_FORMAT_ENGLISH = "ddd, MMMM d";
        public const string MEDIUM_DATE_FORMAT_FRENCH = "ddd, d MMMM";

        public const string LONG_DATE_FORMAT_ENGLISH = "dddd, MMMM dd";
        public const string LONG_DATE_FORMAT_FRENCH = "dddd, d MMMM";
        public const string DISPLAY_DATE_FORMAT = "MM/dd/yyyy";

        // Purolator Domain Language Cookie Name
        public const string COOKIE_NAME_PUROLATOR_DOMAIN_LANGUAGE = "PurolatorLanguage";

        public const string COOKIE_SmartSort_USER_AUTHENICATED = "SmartSortUserAuthenicated";

        //Query string parameter
        public const string QUERY_STRING_PARAMETER_LANGUAGE = "lang";
        public const string LANGUAGE_GLOBLE = "GlobleLanguage";

        public const int MIN_CDN_POSTAL_CODE_LENGTH = 6;
        public const int MAX_CDN_POSTAL_CODE_LENGTH = 6;

        public const string EXCEPTION_CATEGORY_DATA_ACCESS = "Data Access";
        public const string EXCEPTION_CATEGORY_DATA_BUSINESS = "Business Access";

        public const int FROM_ADDRESS_LINE1_MAX_LENGTH = 25;
        public const int FROM_ADDRESS_LINE2_MAX_LENGTH = 25;

        # region Address triaging
        public const string Address_Index_Action = "INDEX";
        public const string Address_Status_Unmatched = "Unmatched";
        public const string Address_Status_Unparssable = "Unparssable";
        public const string PROVINCE = "Province";
        public const string STREET_TYPE = "Street Type";
        public const string DIRECTION = "Direction";
        public const string STREET_SUFFIX = "Suffix";
        public const string Route = "Route";
        public const string Shelf = "Shelf";
        public const string StreetName = "Street Name";
        public const string CURRENT_PAGE_INDEX = "CURRENT_PAGE_INDEX";

        public const string SORTBY_EDD = "ExpectedDeliveryDate";
        public const string SORTBY_CUSTOMER = "Customer";
        public const string SORTBY_ADDRESS = "Address";
        public const string SORTBY_PIECES = "Pieces";
        public const string SORTBY_STATUS = "AddressStatus";
        public const string SORTBY_LAST_UPDATED_BY = "LastUpdatedBy";
        public const string SORTBY_LAST_UPDATED_ON = "LastUpdatedOn";

        public const string SORT_COLUMN = "Sort Column Name";
        public const string SORT_DIRECTION = "Sort Direction";
        public const string NoOfRecords = "";
        public const string ESCALATED_ADDRESS = "Escalated Address";
        public const string ADDRESS_ID = "Address ID";
        public const string ADDRESS_STATUS = "Address Status";
        public const string PIECES_NO = "Pieces";
        public const int Row_Per_Page = 20;
        public const string SPACE = "   ";
        public const int DEFAULTINTEGER_VALUE = 0;
        #endregion

        #region Courier Manifest
        public const string SESSION_MANIFESTMODAL = "ManifestModal";
        public const string DEVICE_ID = "WP";
        #endregion

        # region User Roles

        public const string Roles_System_Administrator = "System Administrator";
        public const string Roles_District_Manager = "District Manager";
        public const string Roles_Engineer = "Engineer";
        public const string Roles_Unit_Manager = "Unit Manager";
        public const string Roles_Address_Triager = "Address Triager";
        public const string Roles_Lead_Hand = "Lead Hand";

        #endregion

        # region DataBase StoredProcedures
        public const string SP_DisableUser = "[dbo].DisableUser";
        public const string SP_UpdateUserProfile = "[dbo].UpdateUserProfile";
        public const string SP_UpdateSAPAccount = "[dbo].[UpdateSAPUserID]";
        public const string SP_GetUsers = "[dbo].[GetUsers]";
        public const string SP_GetTerminals = "[dbo].[GetTerminals]";
        public const string SP_GetUserInfo = "[dbo].[GetUserInfo]";
        public const string SP_GetAssignableRoles = "[dbo].[GetAssignableRoles]";
        public const string SP_GetUserRole = "[dbo].[GetUserRoles]";
        public const string SP_ValidateTerminals = "[dbo].[ValidateTerminal]";
        public const string SP_GetUserTerminals = "[dbo].[GetUserTerminals]";
        public const string SP_CloseRoutes = "[dbo].[CloseRoutes]";
        public const string SP_GetManifestForRoutes = "[dbo].[GetManifestForRoutes]";
        public const string SP_GetPudroAndRoutes = "[dbo].[GetPudroAndRoutes]";
        public const string SP_GetTriageQueue = "[dbo].[GetTriageQueue]";
        public const string SP_GetTriageAddress = "[dbo].[GetAddresses]";
        public const string SP_GetTriagPins = "[dbo].[GetTriagePins]";
        public const string SP_GetTriageReasonCodes = "[dbo].[Gettriagereasoncodes]";
        public const string SP_UpdateTriageAdress = "[dbo].[UpdateTriageAddress]";
        public const string SP_UpdateTriageLastTouch = "[dbo].[UpdateTriageLastTouch]";
        public const string SP_GetTriageReasonCode = "[dbo].[Gettriagereasoncodes]";
        public const string SP_GetAddressFromRoutePlan = "[dbo].[GetAddressFromRoutePlan]";
        public const string SP_GetStreetNameFromRoutePlan = "[dbo].[GetStreetNameFromRoutePlan]";
        public const string SP_GeCustomerNameFromRoutePlan = "[dbo].[GetCustomerNameFromRoutePlan]";
        public const string SP_GetAllRouteAndShelfFromRoutePlan = "[dbo].[GetAllRouteAndShelfFromRoutePlan]";
        public const string SP_UpdateRouteAndShelf = "[dbo].[UpdateRouteAndShelf]";
        public const string SP_GetProvinces = "[dbo].[GetProvinces]";
        public const string SP_GetStreetSuffix = "[dbo].[GetStreetSuffix]";
        public const string SP_GetStreetDirection = "[dbo].[GetStreetDirection]";
        public const string SP_GetStreetTypes = "[dbo].[GetStreetTypes]";
        public const string SP_GetAllRoutes = "[dbo].[GetAllRoutes]";
        public const string SP_GetAllShelfsForRoute = "[dbo].[GetAllShelfsForRoute]";
        public const string SP_GetProfileInfo = "[dbo].[GetProfileInfo]";
        public const string SP_GetOffsetByTerminal = "[dbo].[GetOffsetByTerminal]";
        public const string SP_GetProfileInfoBySAP = "[dbo].[GetUserInfoBySAP]";
        public const string SP_SetProfileInfo = "[dbo].[SetProfileInfo]";
        public const string SP_GetAddressesFromOtherShelves = "[dbo].[GetAddressFromOtherShelf]";
        public const string SP_GetAddressesByRouteShelf = "[dbo].[GetAddressesByRouteShelf]";
        public const string SP_GetActualsAddressesByRouteShelf = "[dbo].[GetActualsAddressesByRouteShelf]";
        public const string SP_LoadBalanceMoveAddr = "[dbo].[LoadBalanceMoveAddr]";
        public const string SP_LoadBalanceReset = "[dbo].[LoadBalanceReset]";
        public const string SP_GetActiveSortPlan = "[dbo].[GetActiveSortPlan]";
        public const string SP_GetSmartSortStats = "[dbo].[GetSmartSortStats]";
        public const string SP_GetMoveHistory = "[dbo].[GetVVRMovingReport]";
        public const string SP_GetRouteShelfByTerminal = "[dbo].[GetRouteShelfByTerminal]";
        public const string SP_InitiateAMShift = "[dbo].[InitiateAMShift]";
        public const string SP_GetVehicleVolumeReport = "[dbo].[GetVehicleVolumeReport]";
        public const string SP_GetAllValidSortPlans = "[dbo].[GetAllValidSortPlans]";
        public const string SP_GetRoutePlansForCalendar = "[dbo].[GetRoutePlansForCalendar]";
        public const string SP_SetActiveSortPlanForDate = "[dbo].[SetActiveSortPlanForDate]";
        public const string SP_CheckAMShift = "[dbo].[CheckAMShift]";
        public const string SP_GetPudroInfo = "[dbo].[GetPudroInfo]";
        public const string SP_GetLineDetails = "[dbo].[GetLineDetails]";
        public const string SP_UpdatePudroInfo = "[dbo].[UpdatePudroInfo]";
        public const string SP_GetActiveRoutePlan = "[dbo].[GetActiveRoutePlan]";
        public const string SP_GetSortPlanForRoutePlan = "[dbo].[GetSortPlanForRoutePlan]";        
        public const string SP_GetSortPlanInfo = "[dbo].[GetSmartSortPlanInfo]";
        public const string SP_UpdateSortPlanInfo = "[dbo].[UpdateSmartSortPlanInfo]";
        public const string SP_UpdateLineDetails = "[dbo].[UpdateLineDetails]";
        public const string SP_DeleteSortPlanSchedule = "[dbo].[DeleteSortPlanSchedule]";
        public const string SP_UpdateSortPlanSchedule = "[dbo].[UpdateSortPlanSchedule]";
        public const string SP_GetSortPlanScheduleDates = "[dbo].[GetSortPlanScheduleDates]";
        public const string SP_GetCourierManifestReport = "[dbo].GetCourierManifestReport";
        //public const string SP_GetCourierManifestReportOnly = "[dbo].GetCourierManifestReportOnly";
        public const string SP_GetPinHistory = "[dbo].[GetPinHistory]";
        public const string SP_GetWorkingDay = "[dbo].[GetWorkingDateByTerminal]";
        public const string SP_GetAddressFromRoutePlanById = "[dbo].[GetAddressByRoutePlanID]";
        public const string SP_GetUnitNumberFromRoutePlan = "[dbo].[GetUnitNumberFromRoutePlan]";        
        public const string SP_GetRoutePlans = "[dbo].[GetRoutePlans]";
        public const string SP_GetAutoStartShiftTime = "[dbo].[GetShiftAutoStartTime]";
        public const string SP_SetAutoStartShiftTime = "[dbo].[SetShiftAutoStartTime]";
        public const string SP_GetWorkingDatesForDailyMoves = "[dbo].[GetWorkingDatesForDailyMoves]";
        public const string SP_ApplyPreviousMoveUpdate = "[dbo].[ApplyMovesFromHistory]";
        public const string SP_DeleteRecordsInTriageQueue = "[dbo].[DeleteRecordsInTriageQueue]";
        public const string SP_GetGlobalConfig = "[dbo].[GetSupportPager]";
        public const string SP_UpdateGlobalConfig = "[dbo].[UpdateSupportPager]";
        
        
        
        
        #endregion

        #region Table Column Names
        public const string ShelfPrefixTableColumName = "s";
        public const string ShelfContentsRegexTableColumName = @"^s(?:\d+|A|B|C|D|PR|DG)$";
        public const string ProjectedStopsTableColumnName = "ProjectedStops";
        public const string ProjectedPiecesTableColumnName = "ProjectedPieces";
        public const string ActualStopsTableColumnName = "ActualStops";
        public const string ActualPiecesTableColumnName = "ActualPieces";
        public const string MinStopsTableColumnName = "MinStops";
        public const string MinPiecesTableColumnName = "MinPieces";
        public const string IsPRGroupingTableColumnName = "IsPRGrouping";
        public const string RoutePlanCodeIdTableColumnName = "RoutePlanCodeID";
        public const string ParkingPlanCodeIdTableColumnName = "ParkingPlanCodeID";
        public const string ParkingPlanCodeTableColumnName = "ParkingPlanCode";
        public const string ParkingPlanDescriptionTableColumnName = "ParkingPlanDescription";
        public const string RoutePlanCodeTableColumnName = "RoutePlanCode";
        public const string SortPlanCodeTableColumnName = "SortPlanCode";
        public const string SortPlanDefaultTableColumnName = "SortPlanDefault";
        public const string IsDefaultSortPlanPresentTableColumnName = "IsDefaultSortPlanPresent";
        public const string SortPlanDescriptionTableColumnName = "SortPlanDescription";
        public const string AllRoutesTableColumnName = "AllRoutes";
        public const string UnassignedRoutesTableColumnName = "unassignedRoute";
        public const string ActiveTableColumnName = "Active";
        public const string LineNumberTableColumnName = "LineNumber";
        public const string IRouteTableColumnName = "IRoute";
        public const string RouteTableColumnName = "Route";
        public const string BeltSideTableColumnName = "BeltSide";
        public const string TruckNumberTableColumnName = "TruckNumber";
        public const string RoutePositionTableColumnName = "Position";
        public const string RouteNameTableColumnName = "Route";
        public const string LastUpdateDateTimeTableColumnName = "LastUpdateDateTime";
        public const string TermConfigIdTableColumnName = "TermConfID";
        public const string TotalVehicleStopsTableColumnName = "TotalVehicleStops";
        public const string TotalCustomerStopsTableColumnName = "TotalCustomerStops";
        public const string TotalPiecesTableColumnName = "TotalPieces";
        public const string IsSortPlanEditable = "IsSortPlanEditable";
        #endregion

        #region Database Value Constants
        public const string DGShelf = "DG";
        public const string PRShelf = "PR";
        public static readonly List<string> SpecialShelves = new List<string>() {{"17"}, {"18"}, {"19"}, {"20"}, {"21"}, {"22"}, {"A"}, {"B"}, {"C"}, {"D"}, {"DG"}, {"PR"} };
        public static readonly List<string> PrDgShelves = new List<string>() { { "DG" }, { "PR" } };
        public const string TrueBitDbValue = "1";
        public const string PiecesDbValue = "Pieces";
        public const string StopsDbValue = "Stops";
        public const string LeftBeltSideDbValue = "L";
        public const string CenterBeltSideDbValue = "C";
        public const string RightBeltSideDbValue = "R";
        public const string YesDbValue = "YES";
        public const string YesShortDbValue = "Y";
        public const string NoDbValue = "NO";
        public const string NoShortDbValue = "N";
        #endregion
         
        #region Sort Plan Calendar


        public const string ALERT_SUCCESS = "alert alert-success";
        public const string ALERT_FAIL = "alert alert-danger";
        public const string SCHEDULE_TYPE_WEEKLY = "w";
        public const string SCHEDULE_TYPE_MONTHLY = "m";
        public const string ASSIGNMENT_TYPE_SCHEDULE = "S";
        public const string ASSIGNMENT_TYPE_MANUAL = "M";

        #endregion Sort Plan Calendar

        #region Terminal Configuration

        public const string NOTASSIGNED = "Not Assigned";
        public const string ASSIGNED = "Assigned";
        public const string CreateMode = "C";
        public const string UpdateMode = "U";
        #endregion


    }
}
