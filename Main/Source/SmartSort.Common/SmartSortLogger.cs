﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Diagnostics;
using System.Configuration;
using Microsoft.Practices.EnterpriseLibrary.Logging;
using Microsoft.Practices.EnterpriseLibrary.Logging.Formatters;
using Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners;
using Microsoft.Practices.EnterpriseLibrary.Common.Configuration;

namespace SmartSort.Common
{
    /// <summary>
    /// Logger Class to add exception in Event Viewer
    /// </summary>
    public class SmartSortLogger
    {

        static SmartSortLogger()
        {
            // create logging folder
            try
            {
                if (!Directory.Exists(@"C:\AppLogs\SmartSort"))
                {
                    Directory.CreateDirectory(@"C:\AppLogs\SmartSort");
                }
            }
            catch 
            {}

            // initialize logging
            IConfigurationSource configurationSource = ConfigurationSourceFactory.Create();
            LogWriterFactory logWriterFactory = new LogWriterFactory(configurationSource);
            Logger.SetLogWriter(logWriterFactory.Create());           
        }


        /// <summary>
        /// Writes Exception/Errors to Event Viewer
        /// </summary>
        /// <param name="message">Error Message/Exception</param>
        /// <param name="category">Log Catergory</param>
        /// <param name="priority">Priority</param>k
        /// <param name="type">Log Type</param>
        public static void Write(string message,
            string category,
            LogPriority priority,
            TraceEventType type)
        {
            //LogWriterFactory logWriterFactory = new LogWriterFactory();
            //LoggingConfiguration loggingConfiguration = BuildProgrammaticConfig();
            //LogWriter logWriter = new LogWriter(loggingConfiguration);
            //logWriter = logWriterFactory.Create();                        
            LogEntry log = new LogEntry();
            try
            {
                log.Message = message;
                log.Categories.Add("WebPortal");
                log.Priority = (int)priority;
                log.Severity = type;
                Logger.Write(log);
            }
            finally
            {
                //Logger.Reset();
            }
        }       

        public static void Error(string message, System.Exception ex)
        {
            LogEntry log = new LogEntry();
            log.Message = message;
            log.Categories.Add("WebPortal");
            log.Priority = (int)LogPriority.High;
            log.Severity = TraceEventType.Error;
            if(ex != null)
            {
                Dictionary<string, object> exProperties = new Dictionary<string, object>();            
                exProperties.Add("ExceptionMessage", ex.Message);
                exProperties.Add("ExceptionStackTrace", ex.StackTrace);
                log.ExtendedProperties = exProperties;
            }
            Logger.Write(log);
        }

       
    }
}