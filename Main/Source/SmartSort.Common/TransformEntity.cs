﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.IO;
using System.Xml;
using System.Xml.Serialization;
using System.Text.RegularExpressions;

namespace SmartSort.Common
{
    public static class TransformEntity
    {
        /// <summary>
        /// Converts a DataTable to a list with generic objects
        /// </summary>
        /// <typeparam name="T">Generic object</typeparam>
        /// <param name="table">DataTable</param>
        /// <returns>List with generic objects</returns>
        public static List<T> DataTableToList<T>(this DataTable table, bool isSingleEntity = false, bool isRegexPropertyBind = false) where T : class, new()
        {
            var dataList = new List<T>();

            if (table != null && table.Rows.Count > 0)
            {
                try
                {

                    BindingFlags flags = BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance | BindingFlags.NonPublic;

                    var objFieldNames = (from PropertyInfo aProp in typeof(T).GetProperties(flags)
                                         let attrInfo = aProp.GetCustomAttribute<DataColumnMetadataAttribute>()
                                         select new
                                         {
                                             IsBooleanMapping = attrInfo != null ? attrInfo.IsBooleanMapping : false,
                                             IsRegexMatch = attrInfo != null ? attrInfo.IsRegexMatch : false,
                                             DataColumnName = attrInfo != null ? attrInfo.DataColumnName.ToLower() : aProp.Name.ToLower(),
                                             Name = aProp.Name.ToLower(),
                                             Type = Nullable.GetUnderlyingType(aProp.PropertyType) ?? aProp.PropertyType,
                                         }).ToList();
                    var dataTblFieldNames = (from DataColumn aHeader in table.Columns
                                             select new
                                             {
                                                 Name = aHeader.ColumnName.ToLower(),
                                                 Type = aHeader.DataType,
                                             }).ToList();
             
                    var commonFields = from one in objFieldNames
                                       join two in dataTblFieldNames on one.DataColumnName equals two.Name
                                       where one.Type == two.Type || one.IsBooleanMapping 
                                       select one;
                   
                    var dataRows = isSingleEntity ? new List<DataRow> { table.Rows[0] } : table.AsEnumerable().ToList();

                    foreach (DataRow dataRow in dataRows)
                    {
                        var aTSource = new T();

                        foreach (var aField in commonFields)
                        {
                            PropertyInfo propertyInfo = aTSource.GetType().GetProperty(aField.Name, flags);

                            if (propertyInfo != null)
                            {
                                var rowValue = (dataRow[aField.DataColumnName] == DBNull.Value) ? null : dataRow[aField.DataColumnName];

                                if (aField.IsBooleanMapping)
                                {
                                    string val = rowValue as string;
                                    
                                    if (val != null)
                                    {
                                        rowValue =  val.Equals(Constants.YesDbValue, StringComparison.OrdinalIgnoreCase) || val.Equals(Constants.YesShortDbValue, StringComparison.OrdinalIgnoreCase);
                                    }
                                    else if (rowValue != null)
                                    {
                                        rowValue = String.Equals(rowValue.ToString(), Constants.TrueBitDbValue);
                                    }
                                }
                                else if (rowValue is string)
                                {
                                    rowValue = (rowValue as string).Trim();
                                }

                                propertyInfo.SetValue(aTSource, rowValue, null);
                            }
                        }

                        if (isRegexPropertyBind)
                        {
                            var objFields = objFieldNames != null ? objFieldNames.FindAll(item => item.IsRegexMatch) : null;
                            var tableFields = (from one in objFieldNames
                                               from two in dataTblFieldNames
                                               where one.IsRegexMatch && Regex.IsMatch(two.Name, one.DataColumnName)
                                               select two);

                            foreach (var objField in objFields)
                            {
                                PropertyInfo propertyInfo = aTSource.GetType().GetProperty(objField.Name, flags);

                                if (propertyInfo != null)
                                {
                                    var dict = new Dictionary<string, int>();

                                    foreach (var tableField in tableFields)
                                    {
                                        if (objField != null && objField.Type == typeof(Dictionary<string, int>))
                                        {
                                            int number = 0;
                                            var rowValue = (dataRow[tableField.Name] == DBNull.Value) ? null : dataRow[tableField.Name];

                                            if (rowValue != null && int.TryParse(rowValue.ToString(), out number))
                                            {
                                                dict.Add(tableField.Name, number);
                                            }
                                        }
                                    }
                                    propertyInfo.SetValue(aTSource, dict, null);
                                }
                            }
                        }
                        dataList.Add(aTSource);
                    }
                }
                catch
                {
                    dataList = null;
                }
            }
            return dataList;
        }

        public static T DataTableToEntity<T>(this DataTable dataTable) where T : class, new()
        {
            List<T> entity = DataTableToList<T>(dataTable, true);
            return entity != null ? entity.FirstOrDefault() : null;
        }


        /// <summary>
        /// ObjectToXML
        /// </summary>
        /// <param name="oObject"></param>
        /// <returns>XML</returns>
        public static string ObjectToXML(Object oObject)
        {
            XmlDocument xmlDoc = new XmlDocument();
            XmlSerializer xmlSerializer = new XmlSerializer(oObject.GetType());
            using (MemoryStream xmlStream = new MemoryStream())
            {
                xmlSerializer.Serialize(xmlStream, oObject);
                xmlStream.Position = 0;
                xmlDoc.Load(xmlStream);
                return xmlDoc.InnerXml;
            }
        }

    }
}