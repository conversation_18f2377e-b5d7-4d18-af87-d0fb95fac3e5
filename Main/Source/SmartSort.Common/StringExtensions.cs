﻿using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace SmartSort.Common
{
    public static class StringExtensions
    {
        public static decimal? ToNullableDecimal(this string value)
        {
            return value.IsDecimal() ? (decimal?)decimal.Parse(value) : null;
        }

        public static bool IsInteger(this string value)
        {
            int result;
            return int.TryParse(value, out result);
        }

        public static int GetInteger(this string value)
        {
            int myInteger = 0;
            if (value.IsInteger())
            {
                myInteger = Convert.ToInt32(value);
            }
            return myInteger;
        }

        public static bool IsDecimal(this string value)
        {
            bool retVal = false;
            try
            {
                if (value.ValueExists())
                {
                    Convert.ToDecimal(value);
                    retVal = true;
                }
            }
            catch
            {
                retVal = false;
            }
            return retVal;
        }

        public static decimal GetDecimal(this string value)
        {
            decimal myDecimal = 0;
            if (value.IsDecimal())
            {
                myDecimal = Convert.ToDecimal(value);
            }
            return myDecimal;
        }

        public static int? ToNullableInteger(this string value)
        {
            return value.IsInteger() ? (int?)int.Parse(value) : null;
        }

        public static bool ValueExists(this string value)
        {
            return (!string.IsNullOrEmpty(value) && value.Trim().Length > 0);
        }

        public static bool ValueEmpty(this string value)
        {
            return (string.IsNullOrEmpty(value) || value.Trim().Length == 0);
        }

        public static string ToMMDDYYYY(this string value)
        {
            if (!string.IsNullOrEmpty(value) && value.Length == 10)
            {
                var dateParts = value.Split('-');
                var formattedDate = dateParts[1] + "-" + dateParts[0] + "-" + dateParts[2];
                return formattedDate;
            }
            return string.Empty;
        }

        public static bool validateDate(this string value)
        {
            if (!string.IsNullOrEmpty(value) && value.Length == 10)
            {
                Regex rxExpression = new Regex("[0-9]{2}/[0-9]{2}/[0-9]{4}");
                DateTime dateTimeSelected = DateTime.Now;

                if (rxExpression.IsMatch(value) && DateTime.TryParseExact(value, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTimeSelected))
                {
                    //Check if the Date range lies within past 4 weeks 
                    if (DateTime.Compare(dateTimeSelected.Date, DateTime.Now.Date) <= 0 && DateTime.Compare(dateTimeSelected.Date, DateTime.Now.AddDays(-28).Date) >= 0)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
            return false;
        }

        //Converts DateTime to '<3letterDayName>,dd/MM/yyyy'
        public static string convertDateTimeToDatePickerFormat(this DateTime value)
        {
            string formattedDate = string.Empty;
            string dayName = string.Empty;
            string day = string.Empty;
            string month = string.Empty;

            if (value != DateTime.MinValue)
            {
                dayName = value.DayOfWeek.ToString();                
                day = ((Convert.ToString(value.Day).Length == 2) ? Convert.ToString(value.Day) : "0" + Convert.ToString(value.Day));
                month = ((Convert.ToString(value.Month).Length == 2) ? Convert.ToString(value.Month) : "0" + Convert.ToString(value.Month));

                formattedDate = dayName.Substring(0, 3) + "," + day + "/" + month + "/" + value.Year;
            }
            return formattedDate;
        }

        // Convert YYYY-MM-dd to dd/MM/YYYY format
        public static string ConvertDateStringToddMMYYYYFormat(this string value)
        {
            string formattedDate = string.Empty;            
            if (value.ValueExists())
            {
                var dateParts = value.Split('-');

                formattedDate = dateParts[2]+ "/" + dateParts[1] + "/" + dateParts[0];
            }
            return formattedDate;
        }
    }
}