﻿using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace SmartSort.Common
{
    public class SmartSortError
    {
        protected List<string> _subName;
        protected string _message;

        /// <summary>
        /// Initializes a new instance of a business error class.
        /// </summary>
        public SmartSortError()
        {
            _message = "SmartSort Error";
            InnerError = null;
            _subName = new List<string>();
            Parameters = new StringDictionary();
        }

        /// <summary>
        /// Initializes a new instance of a business error class with a specified error message. 
        /// </summary>
        /// <param name="message"></param>
        public SmartSortError(string message) : this()
        {
            _message = message;
        }
        /// <summary>
        /// Initializes a new instance of the SmartSortError class with a specified 
        /// error message and a reference to the inner error that is the cause 
        /// of this error. 
        /// </summary>
        /// <param name="message">The message that describes the error.</param>
        /// <param name="innerError">The error that is the cause of the current error.</param>
        public SmartSortError(string message, SmartSortError innerError)
            : this()
        {
            _message = message;
            InnerError = innerError;
        }
        /// <summary>
        /// The business error message.
        /// </summary>
        public string Message
        {
            get { return _message; }
        }
        /// <summary>
        /// The inner error object.
        /// </summary>
        public SmartSortError InnerError { get; set; }

        /// <summary>
        /// The parameters associated with the error. The parameters carry additional information
        /// of the error. The usage of the parameters is for 
        /// displaying the description of the error.
        /// </summary>
        public StringDictionary Parameters { get; set; }

        /// <summary>
        /// The sub name of the business error. It can be used to distinct the same typed 
        /// business error with different description. 
        /// <see>FiledError</see>
        /// </summary>
        public string SubClassName
        {
            get
            {
                return _subName.Aggregate("", (current, t) => current + ("." + t));
            }
        }

        public virtual string MessageKey
        {
            get
            {
                string key = GetType().ToString().Replace("Purolator.SmartSort.Business.Common.Exception", "SmartSortError").Replace(".", "_");
                if (SubClassName != "")
                    key = key + SubClassName.Replace(".", "_");

                return key;
            }
        }
    }
}
