﻿using System;
using System.Linq;
using System.Configuration;
using System.Globalization;

namespace SmartSort.Common
{
    public static class Utility
    {
        public static string GetDatabaseConnectionName()
        {
            return ConfigurationManager.AppSettings["DATABASE"];
        }

        //offshore code - moved
        public static DateTime ParseDate(string date)
        {
            string formattedSelectedDate = date.ToMMDDYYYY();

            DateTime dateTimeSelected;
            try
            {
               dateTimeSelected = DateTime.ParseExact(formattedSelectedDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
            }
            catch
            {
                dateTimeSelected = DateTime.ParseExact(date, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            }
            return dateTimeSelected;
        }

        public static TimeZoneInfo GetTimeZoneInfoByOffset(string offset)
        {
            TimeZoneInfo timeZoneInfo = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(item => String.Compare(item.GetUtcOffset(DateTime.UtcNow).ToString(), 0, offset, 0, offset.Length) == 0) ?? TimeZoneInfo.Local;
            return timeZoneInfo;
        }

        public static DateTimeOffset GetDateTimeOffsetByOffset(string offset)
        {
            TimeZoneInfo timeZoneInfo = GetTimeZoneInfoByOffset(offset);
            DateTimeOffset dateTimeOffset = TimeZoneInfo.ConvertTime(DateTimeOffset.UtcNow, timeZoneInfo);
            return dateTimeOffset;
        }


        public static DateTimeOffset GetDateTimeOffsetByTimeZoneInfo(TimeZoneInfo timeZoneInfo)
        {
            DateTimeOffset dateTimeOffset = TimeZoneInfo.ConvertTime(DateTimeOffset.UtcNow, timeZoneInfo);
            return dateTimeOffset;
        }
    }
}